// Dummy data untuk pods yang digunakan di halaman list dan detail
export const dummyPods = [
  { 
    id: 1, 
    name: 'my-web-server', 
    image: 'nginx:latest',
    status: 'running' as const, 
    cpu: '15.2%', 
    memory: '512MB / 1GB',
    uptime: '2 days 14h',
    url: 'https://my-web-server.sumopod.com',
    port: 80,
    config: { cpu: 1, memory: 1 },
    logs: [
      '2024/01/15 14:35:22 [notice] 1#1: using the "epoll" event method',
      '2024/01/15 14:35:22 [notice] 1#1: nginx/1.25.3',
      '2024/01/15 14:35:22 [notice] 1#1: built by gcc 12.2.0 (Debian 12.2.0-14)',
      '2024/01/15 14:35:22 [notice] 1#1: OS: Linux 5.15.0-91-generic',
      '2024/01/15 14:35:22 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576',
      '2024/01/15 14:35:22 [notice] 1#1: start worker processes',
      '2024/01/15 14:35:22 [notice] 1#1: start worker process 29'
    ]
  },
  { 
    id: 2, 
    name: 'production-db', 
    image: 'postgres:14-alpine',
    status: 'running' as const, 
    cpu: '8.7%', 
    memory: '1.2GB / 2GB',
    uptime: '5 days 8h',
    url: null,
    port: 5432,
    config: { cpu: 2, memory: 2 },
    logs: [
      '2024-01-10 09:15:00.123 UTC [1] LOG:  starting PostgreSQL 14.10 on x86_64-pc-linux-musl',
      '2024-01-10 09:15:00.124 UTC [1] LOG:  listening on IPv4 address "0.0.0.0", port 5432',
      '2024-01-10 09:15:00.124 UTC [1] LOG:  listening on IPv6 address "::", port 5432',
      '2024-01-10 09:15:00.127 UTC [1] LOG:  listening on Unix socket "/var/run/postgresql/.s.PGSQL.5432"',
      '2024-01-10 09:15:00.132 UTC [22] LOG:  database system was shut down at 2024-01-10 09:14:58 UTC',
      '2024-01-10 09:15:00.137 UTC [1] LOG:  database system is ready to accept connections'
    ]
  },
  { 
    id: 3, 
    name: 'cache-server', 
    image: 'redis:7-alpine',
    status: 'running' as const, 
    cpu: '2.3%', 
    memory: '128MB / 512MB',
    uptime: '1 week 3d',
    url: null,
    port: 6379,
    config: { cpu: 1, memory: 1 },
    logs: [
      '1:C 08 Jan 2024 10:20:15.123 # oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo',
      '1:C 08 Jan 2024 10:20:15.123 # Redis version=7.2.4, bits=64, commit=00000000, modified=0, pid=1, just started',
      '1:C 08 Jan 2024 10:20:15.123 # Warning: no config file specified, using the default config.',
      '1:M 08 Jan 2024 10:20:15.124 * monotonic clock: POSIX clock_gettime',
      '1:M 08 Jan 2024 10:20:15.124 * Running mode=standalone, port=6379.',
      '1:M 08 Jan 2024 10:20:15.124 * Server initialized',
      '1:M 08 Jan 2024 10:20:15.125 * Ready to accept connections tcp'
    ]
  },
  { 
    id: 4, 
    name: 'api-service', 
    image: 'node:18-alpine',
    status: 'running' as const, 
    cpu: '12.8%', 
    memory: '384MB / 1GB',
    uptime: '2 weeks 1d',
    url: 'https://api-service.sumopod.com',
    port: 3000,
    config: { cpu: 1, memory: 1 },
    logs: [
      '> api-service@1.2.3 start',
      '> node server.js',
      '',
      'Environment: production',
      'Port: 3000',
      'Database: Connected to PostgreSQL',
      'Redis: Connected to cache server',
      'Server is running on port 3000',
      'API endpoints loaded successfully'
    ]
  },
  { 
    id: 5, 
    name: 'analytics-db', 
    image: 'mongodb:6.0',
    status: 'stopped' as const, 
    cpu: '0%', 
    memory: '0MB / 1GB',
    uptime: '-',
    url: null,
    port: 27017,
    config: { cpu: 1, memory: 1 },
    logs: [
      'MongoDB starting : pid=1 port=27017 dbpath=/data/db 64-bit host=analytics-db',
      'db version v6.0.12',
      'git version: 33e2b2c4e5e5b5e5e5e5e5e5e5e5e5e5e5e5e5e5',
      'OpenSSL version: OpenSSL 3.0.2 15 Mar 2022',
      'allocator: tcmalloc',
      'modules: none',
      'build environment:',
      '    distmod: ubuntu2204'
    ]
  },
  { 
    id: 6, 
    name: 'frontend-app', 
    image: 'nginx:alpine',
    status: 'running' as const, 
    cpu: '3.1%', 
    memory: '96MB / 512MB',
    uptime: '4 days 12h',
    url: 'https://frontend-app.sumopod.com',
    port: 80,
    config: { cpu: 1, memory: 1 },
    logs: [
      '2024/01/11 08:30:15 [notice] 1#1: using the "epoll" event method',
      '2024/01/11 08:30:15 [notice] 1#1: nginx/1.25.3',
      '2024/01/11 08:30:15 [notice] 1#1: start worker processes',
      '2024/01/11 08:30:15 [notice] 1#1: start worker process 28',
      '2024/01/11 08:30:15 [notice] 1#1: start worker process 29'
    ]
  },
  { 
    id: 7, 
    name: 'worker-queue', 
    image: 'python:3.11-slim',
    status: 'running' as const, 
    cpu: '5.4%', 
    memory: '256MB / 512MB',
    uptime: '6 days 2h',
    url: null,
    port: null,
    config: { cpu: 1, memory: 1 },
    logs: [
      'Starting worker queue processor...',
      'Connected to Redis at redis://cache-server:6379',
      'Connected to database at postgresql://production-db:5432/app',
      'Worker queue is ready to process jobs',
      'Listening for jobs on queue: default',
      'Worker started with PID: 1'
    ]
  },
  { 
    id: 8, 
    name: 'monitoring', 
    image: 'grafana/grafana:latest',
    status: 'running' as const, 
    cpu: '7.2%', 
    memory: '312MB / 1GB',
    uptime: '3 days 18h',
    url: 'https://monitoring.sumopod.com',
    port: 3000,
    config: { cpu: 1, memory: 1 },
    logs: [
      'logger=settings t=2024-01-12T10:15:30.123456789Z level=info msg="Starting Grafana" version=10.2.3',
      'logger=server t=2024-01-12T10:15:30.234567890Z level=info msg="HTTP Server Listen" address=[::]:3000 protocol=http',
      'logger=database t=2024-01-12T10:15:30.345678901Z level=info msg="Connecting to DB" dbtype=sqlite3',
      'logger=migrator t=2024-01-12T10:15:30.456789012Z level=info msg="Starting DB migrations"',
      'logger=server t=2024-01-12T10:15:30.567890123Z level=info msg="HTTP Server started"'
    ]
  }
];

// Interface untuk pod dummy data
export interface DummyPod {
  id: number;
  name: string;
  image: string;
  status: 'running' | 'stopped';
  cpu: string;
  memory: string;
  uptime: string;
  url: string | null;
  port: number | null;
  config: {
    cpu: number;
    memory: number;
  };
  logs: string[];
}
