import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Layouts
import MainLayout from '../components/layouts/MainLayout';
import DashboardLayout from '../components/layouts/DashboardLayout';

// Auth
import ProtectedRoute from '../components/auth/ProtectedRoute';

// Feature flags
import { isFeatureEnabled, FeatureFlags } from '../utils/featureFlags';

// Loading component
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// Lazy-loaded pages
const Landing = lazy(() => import('../pages/Landing'));
const Templates = lazy(() => import('../pages/Templates'));
const Login = lazy(() => import('../pages/Login'));
const Register = lazy(() => import('../pages/Register'));
const Dashboard = lazy(() => import('../pages/Dashboard'));
const Pods = lazy(() => import('../pages/Pods'));
const CreatePod = lazy(() => import('../pages/CreatePod'));
const PodDetails = lazy(() => import('../pages/PodDetails'));
const Services = lazy(() => import('../pages/Services'));
const CreateService = lazy(() => import('../pages/CreateService'));
const CreateServiceByType = lazy(() => import('../pages/CreateServiceByType'));
const ServiceDetails = lazy(() => import('../pages/ServiceDetails'));
const VPS = lazy(() => import('../pages/VPS'));
const CreateVPS = lazy(() => import('../pages/CreateVPS'));
const VPSDetails = lazy(() => import('../pages/VPSDetails'));
const Billing = lazy(() => import('../pages/Billing'));
const Payments = lazy(() => import('../pages/Payments'));
const Affiliate = lazy(() => import('../pages/Affiliate'));
const Settings = lazy(() => import('../pages/Settings'));
const Support = lazy(() => import('../pages/Support'));
const AI = lazy(() => import('../pages/AI'));
const AIQuickStartPage = lazy(() => import('../pages/ai/AIQuickStartPage'));
const AIUsagePage = lazy(() => import('../pages/ai/AIUsagePage'));
const AIModelsPage = lazy(() => import('../pages/ai/AIModelsPage'));
const AIKeysPage = lazy(() => import('../pages/ai/AIKeysPage'));
const AITransactionsPage = lazy(() => import('../pages/ai/AITransactionsPage'));

const NotFound = lazy(() => import('../pages/NotFound'));

const AppRoutes = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        {/* Public routes */}
        <Route element={<MainLayout />}>
          <Route path="/" element={
            <Suspense fallback={<LoadingFallback />}>
              <Landing />
            </Suspense>
          } />
          <Route path="/templates" element={
            <Suspense fallback={<LoadingFallback />}>
              <Templates />
            </Suspense>
          } />
          <Route path="/login" element={
            <ProtectedRoute requireAuth={false}>
              <Suspense fallback={<LoadingFallback />}>
                <Login />
              </Suspense>
            </ProtectedRoute>
          } />
          <Route path="/register" element={
            <ProtectedRoute requireAuth={false}>
              <Suspense fallback={<LoadingFallback />}>
                <Register />
              </Suspense>
            </ProtectedRoute>
          } />
        </Route>

        {/* Protected routes */}
        <Route element={
          <ProtectedRoute>
            <DashboardLayout />
          </ProtectedRoute>
        }>
          {/* Default dashboard route - always redirects to services */}
          <Route path="/dashboard" element={<Navigate to="/dashboard/services" replace />} />

          {/* Pod routes - only available if PODS_FEATURE is enabled */}
          {isFeatureEnabled(FeatureFlags.PODS_FEATURE) && (
            <>
              <Route path="/dashboard/pods" element={
                <Suspense fallback={<LoadingFallback />}>
                  <Pods />
                </Suspense>
              } />
              <Route path="/dashboard/pods/:id" element={
                <Suspense fallback={<LoadingFallback />}>
                  <PodDetails />
                </Suspense>
              } />
              <Route path="/dashboard/pods/create" element={
                <Suspense fallback={<LoadingFallback />}>
                  <CreatePod />
                </Suspense>
              } />
            </>
          )}

          {/* Services routes - always available */}
          <Route path="/dashboard/services" element={
            <Suspense fallback={<LoadingFallback />}>
              <Services />
            </Suspense>
          } />
          <Route path="/dashboard/services/create" element={
            <Suspense fallback={<LoadingFallback />}>
              <CreateService />
            </Suspense>
          } />
          <Route path="/dashboard/services/create/:type" element={
            <Suspense fallback={<LoadingFallback />}>
              <CreateServiceByType />
            </Suspense>
          } />
          <Route path="/dashboard/services/:id" element={
            <Suspense fallback={<LoadingFallback />}>
              <ServiceDetails />
            </Suspense>
          } />

          {/* VPS routes - only available if VPS_FEATURE is enabled */}
          {isFeatureEnabled(FeatureFlags.VPS_FEATURE) && (
            <>
              <Route path="/dashboard/vps" element={
                <Suspense fallback={<LoadingFallback />}>
                  <VPS />
                </Suspense>
              } />
              <Route path="/dashboard/vps/create" element={
                <Suspense fallback={<LoadingFallback />}>
                  <CreateVPS />
                </Suspense>
              } />
              <Route path="/dashboard/vps/:id" element={
                <Suspense fallback={<LoadingFallback />}>
                  <VPSDetails />
                </Suspense>
              } />
            </>
          )}

          {/* AI routes - only available if AI_FEATURE is enabled */}
          {isFeatureEnabled(FeatureFlags.AI_FEATURE) && (
            <>
              {/* Redirect /dashboard/ai to /dashboard/ai/quickstart */}
              <Route path="/dashboard/ai" element={<Navigate to="/dashboard/ai/quickstart" replace />} />

              <Route path="/dashboard/ai/quickstart" element={
                <Suspense fallback={<LoadingFallback />}>
                  <AIQuickStartPage />
                </Suspense>
              } />
              <Route path="/dashboard/ai/usage" element={
                <Suspense fallback={<LoadingFallback />}>
                  <AIUsagePage />
                </Suspense>
              } />
              <Route path="/dashboard/ai/models" element={
                <Suspense fallback={<LoadingFallback />}>
                  <AIModelsPage />
                </Suspense>
              } />
              <Route path="/dashboard/ai/keys" element={
                <Suspense fallback={<LoadingFallback />}>
                  <AIKeysPage />
                </Suspense>
              } />
              {/* AI Transactions route - only available if AI_TRANSACTION_TAB is enabled */}
              {isFeatureEnabled(FeatureFlags.AI_TRANSACTION_TAB) && (
                <Route path="/dashboard/ai/transactions" element={
                  <Suspense fallback={<LoadingFallback />}>
                    <AITransactionsPage />
                  </Suspense>
                } />
              )}
            </>
          )}

          <Route path="/dashboard/billing" element={
            <Suspense fallback={<LoadingFallback />}>
              <Billing />
            </Suspense>
          } />
          <Route path="/dashboard/billing/payments" element={
            <Suspense fallback={<LoadingFallback />}>
              <Payments />
            </Suspense>
          } />
          <Route path="/dashboard/affiliate" element={
            <Suspense fallback={<LoadingFallback />}>
              <Affiliate />
            </Suspense>
          } />
          <Route path="/dashboard/settings" element={
            <Suspense fallback={<LoadingFallback />}>
              <Settings />
            </Suspense>
          } />
          <Route path="/dashboard/support" element={
            <Suspense fallback={<LoadingFallback />}>
              <Support />
            </Suspense>
          } />
        </Route>

        {/* 404 route */}
        <Route path="*" element={
          <Suspense fallback={<LoadingFallback />}>
            <NotFound />
          </Suspense>
        } />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;