import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Mail, KeyRound, User, Building2, AlertCircle, CheckCircle } from 'lucide-react';
import Button from '../components/common/Button';
import { useAuth } from '../context/AuthContext';
import GoogleIcon from '../components/icons/GoogleIcon';
import { usePageTitle } from '../hooks/usePageTitle';

const Register = () => {
  usePageTitle('Register');

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
  });
  const [otpCode, setOtpCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [registrationComplete, setRegistrationComplete] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);

  const { requestOtp, verifyOtp, signInWithGoogle } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Handle referral code from URL
  useEffect(() => {
    const refCode = searchParams.get('ref');
    if (refCode) {
      // Store referral code in localStorage
      localStorage.setItem('referralCode', refCode);
      console.log('Referral code stored:', refCode);
    }
  }, [searchParams]);



  const handleGoogleSignIn = async () => {
    setGoogleLoading(true);
    setError('');

    try {
      const { success, error } = await signInWithGoogle('/dashboard/services');

      if (!success && error) {
        setError(error.message || 'Failed to sign in with Google');
      }
      // No need to navigate - the OAuth flow will redirect automatically
    } catch (err) {
      setError('An error occurred during Google sign-in. Please try again.');
      console.error(err);
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otpSent) {
      // Step 1: Request OTP for registration
      if (!formData.firstName || !formData.lastName || !formData.email) {
        setError('Please fill in all required fields');
        return;
      }

      setIsLoading(true);
      setError('');
      setSuccessMessage('');

      try {
        // Create metadata with user profile information
        const metadata = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          company: formData.company || undefined,
          fullName: `${formData.firstName} ${formData.lastName}`,
        };

        // Request OTP with isRegistration=true to create a new user
        const { success, error } = await requestOtp(formData.email, true, metadata);

        if (success) {
          setOtpSent(true);
          setSuccessMessage('A verification code has been sent to your email');
        } else {
          setError(error?.message || 'Failed to send verification code');
        }
      } catch (err) {
        setError('An error occurred during registration. Please try again.');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    } else {
      // Step 2: Verify OTP to complete registration
      if (!otpCode) {
        setError('Please enter the verification code');
        return;
      }

      setIsLoading(true);
      setError('');

      try {
        const { success, error } = await verifyOtp(formData.email, otpCode);

        if (success) {
          setRegistrationComplete(true);
          setSuccessMessage('Registration successful! Your account has been created.');
        } else {
          setError(error?.message || 'Invalid verification code');
        }
      } catch (err) {
        setError('An error occurred during verification. Please try again.');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8 mt-16">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-md">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            {otpSent ? 'Verify your email' : 'Create your account'}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {otpSent
              ? 'Enter the verification code sent to your email'
              : 'Start managing your containers today'
            }
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 border-l-4 border-green-500 p-4 rounded">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <p className="text-sm text-green-700">{successMessage}</p>
            </div>
          </div>
        )}

        {!registrationComplete && !otpSent && (
          <div className="mt-6">
            <Button
              type="button"
              variant="outline"
              className="w-full flex items-center justify-center"
              onClick={handleGoogleSignIn}
              isLoading={googleLoading}
            >
              {!googleLoading && <GoogleIcon className="mr-2" size={20} />}
              Sign up with Google
            </Button>
          </div>
        )}

        {!registrationComplete && !otpSent && (
          <div className="mt-6 relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or sign up with email</span>
            </div>
          </div>
        )}

        {!registrationComplete ? (
          <form className="mt-6 space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              {!otpSent ? (
                <>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                        First Name
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User size={18} className="text-gray-400" />
                        </div>
                        <input
                          id="firstName"
                          name="firstName"
                          type="text"
                          required
                          className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="John"
                          value={formData.firstName}
                          onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                        Last Name
                      </label>
                      <div className="mt-1 relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User size={18} className="text-gray-400" />
                        </div>
                        <input
                          id="lastName"
                          name="lastName"
                          type="text"
                          required
                          className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder="Doe"
                          value={formData.lastName}
                          onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-700">
                      Company Name
                    </label>
                    <div className="mt-1 relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Building2 size={18} className="text-gray-400" />
                      </div>
                      <input
                        id="company"
                        name="company"
                        type="text"
                        className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Acme Inc"
                        value={formData.company}
                        onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      Email address
                    </label>
                    <div className="mt-1 relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail size={18} className="text-gray-400" />
                      </div>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        autoComplete="email"
                        required
                        className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      />
                    </div>
                  </div>
                </>
              ) : (
                <div>
                  <label htmlFor="otpCode" className="block text-sm font-medium text-gray-700">
                    Verification Code
                  </label>
                  <div className="mt-1 relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <KeyRound size={18} className="text-gray-400" />
                    </div>
                    <input
                      id="otpCode"
                      name="otpCode"
                      type="text"
                      autoComplete="one-time-code"
                      required
                      className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="123456"
                      value={otpCode}
                      onChange={(e) => setOtpCode(e.target.value)}
                    />
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Check your email for the verification code
                  </p>
                </div>
              )}
            </div>

            {otpSent && (
              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <button
                    type="button"
                    onClick={() => setOtpSent(false)}
                    className="font-medium text-blue-600 hover:text-blue-500"
                  >
                    Change information
                  </button>
                </div>
                <div className="text-sm">
                  <button
                    type="button"
                    onClick={() => {
                      const metadata = {
                        firstName: formData.firstName,
                        lastName: formData.lastName,
                        company: formData.company || undefined,
                        fullName: `${formData.firstName} ${formData.lastName}`,
                      };
                      requestOtp(formData.email, true, metadata);
                    }}
                    className="font-medium text-blue-600 hover:text-blue-500"
                  >
                    Resend code
                  </button>
                </div>
              </div>
            )}

            <div>
              <Button
                type="submit"
                variant="primary"
                className="w-full"
                isLoading={isLoading}
              >
                {otpSent ? 'Verify Code' : 'Create Account'}
              </Button>
            </div>
          </form>
        ) : (
          <div className="mt-8 text-center">
            <Button
              as={Link}
              to="/login"
              variant="primary"
              className="w-full"
            >
              Go to Login
            </Button>
          </div>
        )}

        {!registrationComplete && (
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500">
                Sign in
              </Link>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Register;