import { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useServices } from '../hooks/useServices';
import Button from '../components/common/Button';
import Switch from '../components/common/Switch';
import Turnstile from '../components/common/Turnstile';
import { supabase } from '../lib/supabase';
import { usePageTitle } from '../hooks/usePageTitle';
import { useToast } from '../context/ToastContext';
import { isFeatureEnabled, FeatureFlags } from '../utils/featureFlags';
import { billingService } from '../services/billingService';
import type { UserBalance } from '../services/billingService';
import { monitorService, type MonitorData, type StorageData } from '../services/monitorService';
import { logsService, type LogsData, formatLogDataWithHighlighting } from '../services/logsService';
import { deploymentService, type DeploymentData, type EnvironmentData } from '../services/deploymentService';
import { formatDateWithTimezone } from '../utils/formatters';
import {
  ArrowLeft,
  ExternalLink,
  RefreshCw,
  Play,
  FileText,
  Terminal,
  Settings,
  Database,
  Loader,
  Link2,
  Trash2,
  Key,
  Activity,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  AlertTriangle,
  X,
  HardDrive
} from 'lucide-react';
import DeleteServiceModal from '../components/modals/DeleteServiceModal';



const ServiceDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { service, loading, error, refreshServices, updateAutoRenewal } = useServices(id);
  const navigate = useNavigate();
  const { addToast } = useToast();
  const [searchParams, setSearchParams] = useSearchParams();

  // Set dynamic page title based on service name
  usePageTitle(service ? `${service.name} - Service Details` : 'Service Details');

  // Tab state from URL
  const activeTab = searchParams.get('tab') as 'access' | 'monitor' | 'logs' | 'upgrade' | 'config' || 'access';

  const setActiveTab = (tab: 'access' | 'monitor' | 'logs' | 'upgrade' | 'config') => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('tab', tab);
      return newParams;
    });
  };

  const [isRestarting, setIsRestarting] = useState(false);
  const [isStarting, setIsStarting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [previousStatus, setPreviousStatus] = useState<string | null>(null);
  const pollingIntervalRef = useRef<number | null>(null);

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Docker version state
  const [selectedDockerVersion, setSelectedDockerVersion] = useState('latest');
  const [dockerImageName, setDockerImageName] = useState('');
  const [isLoadingDockerVersion, setIsLoadingDockerVersion] = useState(false);

  // Available upgrades state
  const [availableUpgrades, setAvailableUpgrades] = useState<any[]>([]);
  const [isLoadingUpgrades, setIsLoadingUpgrades] = useState(false);

  // Upgrade confirmation modal state
  const [upgradeModal, setUpgradeModal] = useState<{
    isOpen: boolean;
    targetPlan?: string;
    targetPrice?: number;
    proratedCost?: number;
    resourceChanges?: string;
    unusedBalance?: number;
    finalCost?: number;
    selectedUpgrade?: any;
  }>({
    isOpen: false
  });

  // Renewal confirmation modal state
  const [renewalModal, setRenewalModal] = useState<{
    isOpen: boolean;
    currentExpiry?: string;
    newExpiry?: string;
    renewalCost?: number;
  }>({
    isOpen: false
  });

  // Environment variable edit modal state
  const [envVarModal, setEnvVarModal] = useState<{
    isOpen: boolean;
    envContent?: string;
  }>({
    isOpen: false
  });

  // Docker version edit modal state
  const [dockerVersionModal, setDockerVersionModal] = useState<{
    isOpen: boolean;
    version?: string;
  }>({
    isOpen: false
  });

  // Environment update success popup state
  const [showEnvUpdateSuccess, setShowEnvUpdateSuccess] = useState(false);

  // Docker version update success popup state
  const [showDockerUpdateSuccess, setShowDockerUpdateSuccess] = useState(false);

  // Deployment logs modal state
  const [deploymentLogsModal, setDeploymentLogsModal] = useState<{
    isOpen: boolean;
    deploymentId?: string;
    deploymentName?: string;
    status?: string;
    logs?: string;
  }>({
    isOpen: false
  });

  // Auto renewal update state
  const [isUpdatingAutoRenewal, setIsUpdatingAutoRenewal] = useState(false);

  // Renewal processing state
  const [isProcessingRenewal, setIsProcessingRenewal] = useState(false);

  // Upgrade processing state
  const [isProcessingUpgrade, setIsProcessingUpgrade] = useState(false);

  // User balance state
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);

  // Reset password modal state (for n8n services)
  const [resetPasswordModal, setResetPasswordModal] = useState<{
    isOpen: boolean;
  }>({
    isOpen: false
  });

  // Reset password processing state
  const [isProcessingResetPassword, setIsProcessingResetPassword] = useState(false);

  // Turnstile state
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  const [turnstileKey, setTurnstileKey] = useState(0);

  // Monitoring data state
  const [monitorData, setMonitorData] = useState<MonitorData | null>(null);
  const [isLoadingMonitorData, setIsLoadingMonitorData] = useState(false);

  // Storage data state
  const [storageData, setStorageData] = useState<StorageData | null>(null);
  const [isLoadingStorageData, setIsLoadingStorageData] = useState(false);

  // Logs data state
  const [logsData, setLogsData] = useState<LogsData | null>(null);
  const [isLoadingLogsData, setIsLoadingLogsData] = useState(false);

  // Deployment data state
  const [deploymentData, setDeploymentData] = useState<DeploymentData[]>([]);
  const [isLoadingDeploymentData, setIsLoadingDeploymentData] = useState(false);

  // Environment variables data state
  const [environmentData, setEnvironmentData] = useState<EnvironmentData | null>(null);
  const [isLoadingEnvironmentData, setIsLoadingEnvironmentData] = useState(false);

  // Stable callback functions for Turnstile to prevent re-rendering loops
  const handleTurnstileVerify = useCallback((token: string) => {
    setTurnstileToken(token);
  }, []);

  const handleTurnstileError = useCallback(() => {
    setTurnstileToken(null);
  }, []);

  const handleTurnstileExpire = useCallback(() => {
    setTurnstileToken(null);
  }, []);

  // Fetch user balance
  const fetchUserBalance = async () => {
    try {
      setIsLoadingBalance(true);
      const balance = await billingService.getUserBalance();
      setUserBalance(balance);
    } catch (error) {
      console.error('Error fetching user balance:', error);
    } finally {
      setIsLoadingBalance(false);
    }
  };

  // Effect to fetch user balance when component mounts
  useEffect(() => {
    fetchUserBalance();
  }, []);

  // Fetch monitoring data
  const fetchMonitoringData = async () => {
    if (!service?.id) return;

    try {
      setIsLoadingMonitorData(true);
      const data = await monitorService.getServiceMonitorData(service.id);
      setMonitorData(data);
    } catch (error) {
      console.error('Error fetching monitoring data:', error);
      setMonitorData(null);
    } finally {
      setIsLoadingMonitorData(false);
    }
  };

  // Fetch storage data
  const fetchStorageData = async () => {
    if (!service?.id) return;

    try {
      setIsLoadingStorageData(true);
      const data = await monitorService.getServiceStorageData(service.id);
      setStorageData(data);
    } catch (error) {
      console.error('Error fetching storage data:', error);
      setStorageData(null);
    } finally {
      setIsLoadingStorageData(false);
    }
  };

  // Fetch logs data
  const fetchLogsData = async () => {
    if (!service?.id) return;

    try {
      setIsLoadingLogsData(true);
      const data = await logsService.getServiceLogs(service.id);
      setLogsData(data);
    } catch (error) {
      console.error('Error fetching logs data:', error);
      setLogsData(null);
    } finally {
      setIsLoadingLogsData(false);
    }
  };

  // Fetch deployment data
  const fetchDeploymentData = async () => {
    if (!service?.id) return;

    try {
      setIsLoadingDeploymentData(true);
      const data = await deploymentService.getServiceDeployments(service.id);
      setDeploymentData(data);
    } catch (error) {
      console.error('Error fetching deployment data:', error);
      setDeploymentData([]);
    } finally {
      setIsLoadingDeploymentData(false);
    }
  };

  // Fetch environment variables data
  const fetchEnvironmentData = async () => {
    if (!service?.id) return;

    try {
      setIsLoadingEnvironmentData(true);
      const data = await deploymentService.getServiceEnvironmentVariables(service.id);
      setEnvironmentData(data);
    } catch (error) {
      console.error('Error fetching environment data:', error);
      setEnvironmentData(null);
    } finally {
      setIsLoadingEnvironmentData(false);
    }
  };

  // Effect to fetch monitoring data when service is loaded and on monitor tab
  useEffect(() => {
    if (service?.id && activeTab === 'monitor') {
      fetchMonitoringData();
      fetchStorageData();
    }
  }, [service?.id, activeTab]);

  // Effect to fetch logs data when service is loaded and on logs tab
  useEffect(() => {
    if (service?.id && activeTab === 'logs') {
      fetchLogsData();
    }
  }, [service?.id, activeTab]);

  // Effect to fetch deployment data when service is loaded and on config tab
  useEffect(() => {
    if (service?.id && activeTab === 'config') {
      fetchDeploymentData();
      fetchEnvironmentData();
    }
  }, [service?.id, activeTab]);

  // Fetch available upgrades
  const fetchAvailableUpgrades = async () => {
    if (!service?.id) return;

    try {
      setIsLoadingUpgrades(true);

      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error('No session found for fetching upgrades');
        return;
      }

      const response = await fetch(`https://api-gate.sumopod.com/webhook/sumopod/services/upgrade/list?service_id=${service.id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (response.ok) {
        const upgrades = await response.json();
        setAvailableUpgrades(upgrades);
      } else {
        console.error('Failed to fetch available upgrades:', response.status);
        setAvailableUpgrades([]);
      }
    } catch (error) {
      console.error('Error fetching available upgrades:', error);
      setAvailableUpgrades([]);
    } finally {
      setIsLoadingUpgrades(false);
    }
  };

  // Effect to fetch available upgrades when service is loaded
  useEffect(() => {
    if (service?.id) {
      fetchAvailableUpgrades();
    }
  }, [service?.id]);

  // Effect to fetch Docker version when service is loaded
  useEffect(() => {
    if (service?.id) {
      fetchDockerVersion();
    }
  }, [service?.id]);

  // Effect to start polling when service is in provisioning state
  useEffect(() => {
    if (service && service.status === 'provisioning') {
      // Set previous status on first load
      if (previousStatus === null) {
        setPreviousStatus(service.status);
      }

      // Start polling every 1 second
      const startPolling = () => {
        // Clear any existing interval
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }

        // Set new interval
        pollingIntervalRef.current = window.setInterval(checkServiceStatus, 1000);
      };

      startPolling();

      // Cleanup function to clear interval when component unmounts or status changes
      return () => {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
      };
    } else if (pollingIntervalRef.current) {
      // If service is not in provisioning state but we have an interval, clear it
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, [service, previousStatus, id]);

  // Function to check service status
  const checkServiceStatus = async () => {
    if (!id) return;

    try {
      const { data, error } = await supabase
        .from('services')
        .select('status')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error checking service status:', error);
        return;
      }

      // If status has changed from provisioning to something else, refresh the page
      if (data && previousStatus === 'provisioning' && data.status !== 'provisioning') {
        window.location.reload();
      }

      // Update previous status
      if (data && data.status !== previousStatus) {
        setPreviousStatus(data.status);
      }
    } catch (err) {
      console.error('Error in status check:', err);
    }
  };



  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Calculate target price based on current billing plan
  const calculateTargetPrice = (templateBasePrice: number, currentPlan: string) => {
    switch (currentPlan) {
      case 'yearly':
        return templateBasePrice * 12; // Convert monthly base price to yearly
      case 'quarterly':
        return templateBasePrice * 3;
      case 'biannual':
        return templateBasePrice * 6;
      default:
        return templateBasePrice; // monthly
    }
  };

  // Calculate remaining days and pro-rated cost
  const calculateProRatedCost = (currentPrice: number, targetTemplatePrice: number, expiryDate: string) => {
    if (!service) return { remainingDays: 1, totalDays: 30, proratedCost: 0 };

    const now = new Date();
    const expiry = new Date(expiryDate);

    // Calculate remaining days (minimum 1 day)
    const diffTime = Math.max(expiry.getTime() - now.getTime(), 24 * 60 * 60 * 1000); // minimum 1 day
    const remainingDays = Math.max(Math.ceil(diffTime / (1000 * 60 * 60 * 24)), 1);

    // Calculate total days in billing cycle
    const totalDays = service.plan === 'yearly' ? 365 :
                     service.plan === 'quarterly' ? 90 :
                     service.plan === 'biannual' ? 180 : 30; // default monthly

    // Calculate target price based on current billing plan
    const targetPrice = calculateTargetPrice(targetTemplatePrice, service.plan);

    // Calculate pro-rated cost (difference between plans for remaining days)
    const priceDifference = targetPrice - currentPrice;
    const proratedCost = Math.ceil((priceDifference * remainingDays) / totalDays);

    return {
      remainingDays,
      totalDays,
      proratedCost: Math.max(proratedCost, Math.ceil(priceDifference / totalDays)) // minimum 1 day charge
    };
  };

  // Handle upgrade button click
  const handleUpgradeClick = (upgrade: any) => {
    if (!service) return;

    setUpgradeModal({
      isOpen: true,
      targetPlan: upgrade.name,
      targetPrice: upgrade.base_price,
      proratedCost: upgrade.upgrade_fee,
      resourceChanges: `${upgrade.cpu_limit} CPU • ${Math.round(upgrade.ram_limit)}MB RAM`,
      unusedBalance: 0, // Not used from API response
      finalCost: upgrade.upgrade_fee, // Same as upgrade_fee since no pro-rata breakdown
      selectedUpgrade: upgrade // Store the selected upgrade object
    });

    // Fetch latest balance when opening upgrade modal
    fetchUserBalance();

    // Reset Turnstile token and force re-render when opening modal
    setTurnstileToken(null);
    setTurnstileKey(prev => prev + 1);
  };

  // Calculate new expiry date for renewal
  const calculateRenewalExpiry = (currentExpiry: string, plan: string) => {
    const currentExpiryDate = new Date(currentExpiry);
    const newExpiryDate = new Date(currentExpiryDate);

    switch (plan) {
      case 'monthly':
        newExpiryDate.setMonth(newExpiryDate.getMonth() + 1);
        break;
      case 'quarterly':
        newExpiryDate.setMonth(newExpiryDate.getMonth() + 3);
        break;
      case 'biannual':
        newExpiryDate.setMonth(newExpiryDate.getMonth() + 6);
        break;
      case 'yearly':
        newExpiryDate.setFullYear(newExpiryDate.getFullYear() + 1);
        break;
      default:
        newExpiryDate.setMonth(newExpiryDate.getMonth() + 1); // default monthly
    }

    return newExpiryDate.toISOString();
  };

  // Format date for display
  const formatDateForDisplay = (dateString: string) => {
    if (!dateString || dateString.trim() === '') {
      return 'Invalid Date';
    }

    try {
      return formatDateWithTimezone(dateString, { dateFormat: 'long' });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Format deployment date for display
  const formatDeploymentDate = (dateString: string) => {
    if (!dateString || dateString.trim() === '') {
      return { timeAgo: 'Unknown', formattedDate: 'Invalid Date' };
    }

    try {
      // Handle different date formats from Supabase
      let date: Date;

      // Check if it's already in ISO format (ends with Z)
      if (dateString.includes('Z')) {
        date = new Date(dateString);
      }
      // Handle PostgreSQL timestamp format (YYYY-MM-DD HH:MM:SS.ssssss+00)
      else if (dateString.includes('+00')) {
        // Replace space and +00 with Z for proper ISO format
        const isoString = dateString.replace(/\s?\+00$/, 'Z').replace(' ', 'T');
        date = new Date(isoString);
      }
      // Handle PostgreSQL timestamp format without timezone (YYYY-MM-DD HH:MM:SS.ssssss)
      else if (dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)) {
        // Convert to ISO format and assume UTC
        const isoString = dateString.replace(' ', 'T') + 'Z';
        date = new Date(isoString);
      }
      // Handle other formats by trying to parse directly
      else {
        // Try adding Z if it looks like UTC timestamp
        date = new Date(dateString + (dateString.includes('T') ? 'Z' : ''));
      }

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return { timeAgo: 'Unknown', formattedDate: 'Invalid Date' };
      }

      const now = new Date();
      const diffTime = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
      const diffMinutes = Math.floor(diffTime / (1000 * 60));

      let timeAgo = '';
      if (diffDays > 0) {
        timeAgo = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
      } else if (diffHours > 0) {
        timeAgo = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
      } else if (diffMinutes > 0) {
        timeAgo = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
      } else {
        timeAgo = 'Just now';
      }

      const formattedDate = date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      }) + ' ' + date.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit'
      });

      return { timeAgo, formattedDate };
    } catch (error) {
      console.error('Error formatting deployment date:', error);
      return { timeAgo: 'Unknown', formattedDate: 'Invalid Date' };
    }
  };

  // Get deployment status icon and color
  const getDeploymentStatusIcon = (status: string) => {
    switch (status) {
      case 'done':
        return { icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-100', textColor: 'text-green-800' };
      case 'failed':
        return { icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-100', textColor: 'text-red-800' };
      case 'in_progress':
        return { icon: Clock, color: 'text-blue-500 animate-pulse', bgColor: 'bg-blue-100', textColor: 'text-blue-800' };
      case 'warning':
        return { icon: AlertCircle, color: 'text-yellow-500', bgColor: 'bg-yellow-100', textColor: 'text-yellow-800' };
      default:
        return { icon: Clock, color: 'text-gray-500', bgColor: 'bg-gray-100', textColor: 'text-gray-800' };
    }
  };

  // Handle renewal button click
  const handleRenewalClick = () => {
    if (!service) return;

    const newExpiry = calculateRenewalExpiry(service.expires_at, service.plan);

    setRenewalModal({
      isOpen: true,
      currentExpiry: service.expires_at,
      newExpiry: newExpiry,
      renewalCost: service.price
    });

    // Fetch latest balance when opening renewal modal
    fetchUserBalance();

    // Reset Turnstile token and force re-render when opening modal
    setTurnstileToken(null);
    setTurnstileKey(prev => prev + 1);
  };

  // Handle environment variable edit
  const handleEditEnvVar = () => {
    setEnvVarModal({
      isOpen: true,
      envContent: environmentData?.env || ''
    });
  };

  // Fetch Docker version from API
  const fetchDockerVersion = async () => {
    if (!service) return;

    try {
      setIsLoadingDockerVersion(true);

      // Get the current session for authentication
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.error('No session found for fetching Docker version');
        return;
      }

      // Call the API to get Docker version
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/get-version', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          service_id: service.id
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // API returns {version: string, name: string}
      // Example: {"version": "noweb-2025.6.3", "name": "devlikeapro/waha-plus"}
      // If version is null, treat as 'latest'
      const version = result.version || 'latest';
      const imageName = result.name || '';

      setSelectedDockerVersion(version);
      setDockerImageName(imageName);

    } catch (error) {
      console.error('Error fetching Docker version:', error);
      // Show popup error message
      addToast('error', 'Failed to get Image Version', 'Unable to fetch current Docker version.');
      // Keep default 'latest' or show 'Unknown'
      setSelectedDockerVersion('Unknown');
    } finally {
      setIsLoadingDockerVersion(false);
    }
  };

  // Handle Docker version edit
  const handleEditDockerVersion = () => {
    setDockerVersionModal({
      isOpen: true,
      version: selectedDockerVersion
    });
  };

  // Handle Docker version save
  const handleSaveDockerVersion = async (version: string) => {
    if (!service) return;

    try {
      // Get the current session for authentication
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        addToast('error', 'Authentication Error', 'Please log in to update Docker version.');
        return;
      }

      // Call the API to update Docker version
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/update-version', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          service_id: service.id,
          version: version
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.message === 'ok') {
        // Close modal
        setDockerVersionModal({ isOpen: false });

        // Refresh Docker version data to get latest values from server
        await fetchDockerVersion();

        // Also refresh service data to ensure everything is up to date
        refreshServices();

        // Show success popup with instruction to restart service
        setShowDockerUpdateSuccess(true);

        addToast('success', 'Version Updated', `Docker version updated to ${version} successfully.`);
      } else {
        throw new Error('Unexpected response from server');
      }
    } catch (error) {
      console.error('Error saving Docker version:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      addToast('error', 'Update Failed', `Failed to update Docker version: ${errorMessage}`);
    }
  };

  // Handle environment variable save
  const handleSaveEnvVar = async (envContent: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/update-env', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          service_id: id,
          env: envContent
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update environment variables');
      }

      // Close modal
      setEnvVarModal({ isOpen: false });

      // Refresh environment data to get latest values
      await fetchEnvironmentData();

      // Also refresh service data to ensure everything is up to date
      refreshServices();

      // Show success popup with instruction to save and deploy
      setShowEnvUpdateSuccess(true);
    } catch (error) {
      console.error('Error saving environment variables:', error);
      alert('Failed to save environment variables');
    }
  };

  // Handle deployment logs view
  const handleViewDeploymentLogs = (deploymentId: string, deploymentName: string, status: string, logs?: string) => {
    setDeploymentLogsModal({
      isOpen: true,
      deploymentId,
      deploymentName,
      status,
      logs
    });
  };

  // We're now using the direct service properties for URLs

  // Mock handlers for service actions
  const handleStart = () => {
    setIsStarting(true);
    setTimeout(() => {
      setIsStarting(false);
      // In a real app, this would update the service status via API
    }, 1500);
  };



  const handleRestart = async () => {
    if (!service) return;

    setIsRestarting(true);

    try {
      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        addToast('error', 'Authentication Required', 'Please log in to restart your service.');
        return;
      }

      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/restart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          service_id: service.id
        }),
      });

      if (response.ok) {
        // Success - show success message
        addToast('success', 'Service Restarted', 'Your service has been successfully restarted.');

        // Refresh service data to show updated status
        refreshServices();
      } else {
        // Handle error responses
        const errorData = await response.json();
        addToast('error', 'Restart Failed', errorData.message || 'Failed to restart service.');
      }
    } catch (error) {
      console.error('Error restarting service:', error);
      addToast('error', 'Restart Failed', 'An error occurred while restarting the service.');
    } finally {
      setIsRestarting(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    // Use the refreshServices function to fetch the latest data
    refreshServices();

    // Also refresh monitoring data if on monitor tab
    if (activeTab === 'monitor') {
      fetchMonitoringData();
      fetchStorageData();
    }

    // Also refresh logs data if on logs tab
    if (activeTab === 'logs') {
      fetchLogsData();
    }

    // Also refresh deployment and environment data if on config tab
    if (activeTab === 'config') {
      fetchDeploymentData();
      fetchEnvironmentData();
    }

    // Reset the refreshing state after a short delay to show the loading indicator
    setTimeout(() => {
      setIsRefreshing(false);
    }, 500);
  };

  // Handle auto renewal toggle
  const handleAutoRenewalToggle = async (currentValue: boolean) => {
    if (!service) return;

    const newValue = !currentValue;
    setIsUpdatingAutoRenewal(true);

    try {
      await updateAutoRenewal(service.id, newValue);
      addToast('success', 'Auto Renewal Updated', `Auto renewal has been ${newValue ? 'enabled' : 'disabled'} for this service.`);
    } catch (error: any) {
      addToast('error', 'Update Failed', error.message || 'Failed to update auto renewal setting.');
    } finally {
      setIsUpdatingAutoRenewal(false);
    }
  };

  // Handle service renewal
  const handleServiceRenewal = async () => {
    if (!service) return;

    // Check if Turnstile token is available
    if (!turnstileToken) {
      addToast('error', 'Verification Required', 'Please complete the security verification.');
      return;
    }

    setIsProcessingRenewal(true);

    try {
      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        addToast('error', 'Authentication Required', 'Please log in to renew your service.');
        return;
      }

      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/renew', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          service_id: service.id,
          turnstile_token: turnstileToken
        }),
      });

      if (response.ok) {
        // Success - close modal and show success message
        setRenewalModal({ isOpen: false });
        addToast('success', 'Service Renewed', 'Your service has been successfully renewed.');

        // Reset Turnstile token
        setTurnstileToken(null);

        // Refresh service data to show updated expiry date
        refreshServices();

        // Refresh user balance since it was deducted for the renewal
        fetchUserBalance();
      } else {
        // Handle error responses
        const errorData = await response.json();

        if (response.status === 400) {
          if (errorData.code === 'NOT_FOUND') {
            addToast('error', 'Service Not Found', 'The service could not be found.');
          } else if (errorData.code === 'NOT_ENOUGH_BALANCE') {
            addToast('error', 'Insufficient Balance', 'You do not have enough balance to renew this service.');
          } else if (errorData.code === 'CAPTCHA_ERROR') {
            addToast('error', 'Verification Failed', 'Security verification failed. Please try again.');
            // Reset Turnstile token to force re-verification
            setTurnstileToken(null);
            setTurnstileKey(prev => prev + 1);
          } else {
            addToast('error', 'Renewal Failed', errorData.message || 'Failed to renew service.');
          }
        } else {
          addToast('error', 'Renewal Failed', 'An error occurred while renewing the service.');
        }
      }
    } catch (error) {
      console.error('Error renewing service:', error);
      addToast('error', 'Renewal Failed', 'An error occurred while renewing the service.');
      // Reset Turnstile token on error
      setTurnstileToken(null);
    } finally {
      setIsProcessingRenewal(false);
    }
  };

  // Handle service upgrade
  const handleServiceUpgrade = async () => {
    if (!service) return;

    // Check if Turnstile token is available
    if (!turnstileToken) {
      addToast('error', 'Verification Required', 'Please complete the security verification.');
      return;
    }

    setIsProcessingUpgrade(true);

    try {
      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        addToast('error', 'Authentication Required', 'Please log in to upgrade your service.');
        return;
      }

      // Get the selected upgrade from modal state
      const selectedUpgrade = upgradeModal.selectedUpgrade;
      if (!selectedUpgrade) {
        addToast('error', 'Upgrade Error', 'Selected upgrade plan not found.');
        return;
      }

      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          service_id: service.id,
          service_template_id: selectedUpgrade.id,
          turnstile_token: turnstileToken
        }),
      });

      if (response.ok) {
        // Success - close modal and show success message
        setUpgradeModal({ isOpen: false });
        addToast('success', 'Service Upgraded', 'Your service has been successfully upgraded.');

        // Reset Turnstile token
        setTurnstileToken(null);

        // Refresh service data to show updated plan
        refreshServices();

        // Refresh available upgrades list since the service plan has changed
        fetchAvailableUpgrades();

        // Refresh user balance since it was deducted for the upgrade
        fetchUserBalance();
      } else {
        // Handle error responses
        const errorData = await response.json();

        if (response.status === 400) {
          if (errorData.code === 'NOT_FOUND') {
            addToast('error', 'Service Not Found', 'The service could not be found.');
          } else if (errorData.code === 'NOT_ENOUGH_BALANCE') {
            addToast('error', 'Insufficient Balance', 'You do not have enough balance to upgrade this service.');
          } else if (errorData.code === 'CAPTCHA_ERROR') {
            addToast('error', 'Verification Failed', 'Security verification failed. Please try again.');
            // Reset Turnstile token to force re-verification
            setTurnstileToken(null);
            setTurnstileKey(prev => prev + 1);
          } else {
            addToast('error', 'Upgrade Failed', errorData.message || 'Failed to upgrade service.');
          }
        } else {
          addToast('error', 'Upgrade Failed', 'An error occurred while upgrading the service.');
        }
      }
    } catch (error) {
      console.error('Error upgrading service:', error);
      addToast('error', 'Upgrade Failed', 'An error occurred while upgrading the service.');
      // Reset Turnstile token on error
      setTurnstileToken(null);
    } finally {
      setIsProcessingUpgrade(false);
    }
  };

  // Handle n8n password reset
  const handleResetPassword = async () => {
    if (!service) return;

    setIsProcessingResetPassword(true);

    try {
      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        addToast('error', 'Authentication Required', 'Please log in to reset password.');
        return;
      }

      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/n8n/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          service_id: service.id
        }),
      });

      if (response.ok) {
        // Success - close modal and show success message
        setResetPasswordModal({ isOpen: false });
        addToast('success', 'Password Reset Successful', 'Email and password have been successfully reset. Please wait 1-2 minutes, then open your n8n URL to enter new email and password.');
      } else {
        // Handle error responses
        const errorData = await response.json();
        addToast('error', 'Reset Failed', errorData.message || 'Failed to reset password.');
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      addToast('error', 'Reset Failed', 'An error occurred while resetting the password.');
    } finally {
      setIsProcessingResetPassword(false);
    }
  };

  // Check if service is n8n type
  const isN8nService = service?.service_templates?.type === 'n8n' || service?.service_templates?.name?.toLowerCase() === 'n8n';


  if (loading) {
    return (
      <div className="space-y-6">
        {/* Skeleton for header */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <div className="mr-4">
                <div className="h-9 w-20 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div>
                <div className="h-8 w-48 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-9 w-24 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-9 w-24 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Skeleton for service details */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-4"></div>
          <div className="space-y-4">
            <div className="h-24 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-24 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-24 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-4">Error loading service details</div>
          <Button variant="secondary" onClick={() => navigate('/dashboard/services')}>
            Back to Services
          </Button>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-gray-500 mb-4">Service not found</div>
          <Button variant="secondary" onClick={() => navigate('/dashboard/services')}>
            Back to Services
          </Button>
        </div>
      </div>
    );
  }



  return (
    <div className="space-y-6">
      {/* Enhanced header with back button, service name, and key details */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex items-center mb-4 md:mb-0">
            <Link to="/dashboard/services" className="mr-4">
              <Button variant="secondary" size="sm">
                <ArrowLeft size={16} className="mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900">{service.name}</h1>
                <span className={`ml-3 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  service.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : service.status === 'suspended'
                    ? 'bg-yellow-100 text-yellow-800'
                    : service.status === 'stopped'
                    ? 'bg-red-100 text-red-800'
                    : service.status === 'provisioning'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {service.status}
                </span>
              </div>
              <p className="mt-1 text-sm text-gray-500">
                {service.service_templates?.name || 'Service'} • {service.plan} • {formatPrice(service.price)}
              </p>
            </div>
          </div>

          {/* Quick action buttons in header */}
          {service.status !== 'provisioning' && (
            <div className="flex items-center space-x-2">
              <Button
                variant="danger"
                size="sm"
                onClick={() => setIsDeleteModalOpen(true)}
              >
                <Trash2 size={16} className="mr-2" />
                Delete
              </Button>

              {service.status !== 'active' && service.status !== 'suspended' && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleStart}
                  isLoading={isStarting}
                  disabled={isStarting || isRestarting || loading}
                >
                  <Play size={16} className="mr-2" />
                  {isStarting ? 'Starting...' : 'Start'}
                </Button>
              )}
              <Button
                variant="secondary"
                size="sm"
                onClick={handleRestart}
                isLoading={isRestarting}
                disabled={isRestarting || isStarting || loading}
              >
                <RefreshCw size={16} className={`mr-2 ${isRestarting ? 'animate-spin' : ''}`} />
                {isRestarting ? 'Restarting...' : 'Restart'}
              </Button>

              {/* Reset Password button for n8n services */}
              {isN8nService && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setResetPasswordModal({ isOpen: true })}
                  disabled={isProcessingResetPassword}
                >
                  <Key size={16} className="mr-2" />
                  Reset Password
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Service Status - Only show when provisioning */}
      {service.status === 'provisioning' && (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex flex-col items-center justify-center py-6">
            <div className="mb-6">
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                <Loader className="h-8 w-8 text-blue-600 animate-spin" />
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-900 mb-2">Provisioning Your Service</h3>
            <p className="text-sm text-gray-500 mb-6 text-center max-w-md">
              Preparing your {service.service_templates?.name || 'service'}. This may take a few minutes.
              Your service will be ready to use soon.
            </p>

            <div className="flex space-x-3">
              <Button
                variant="primary"
                size="sm"
                onClick={handleRefresh}
                isLoading={isRefreshing || loading}
                disabled={isRefreshing || loading}
              >
                <RefreshCw size={16} className={`mr-2 ${isRefreshing || loading ? 'animate-spin' : ''}`} />
                {isRefreshing || loading ? 'Refreshing...' : 'Refresh Status'}
              </Button>
              <Button
                as={Link}
                to="/dashboard/services"
                variant="secondary"
                size="sm"
              >
                View All Services
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Suspended Service Warning Banner */}
      {service && service.status === 'suspended' && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-lg font-medium text-red-800 mb-2">
                ⚠️ Service Suspended - Scheduled for Deletion
              </h3>
              <div className="text-sm text-red-700 mb-4">
                <p className="mb-2">
                  Your service has been suspended and will be permanently deleted on{' '}
                  <strong>{service.schedule_delete_at ? formatDateForDisplay(service.schedule_delete_at) : 'Unknown'}</strong>.
                </p>
                <p className="mb-2">
                  All data associated with this service will be lost after the deletion date.
                </p>
                <p>
                  To prevent deletion and reactivate your service, please renew your subscription immediately.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleRenewalClick}
                  className="bg-red-600 hover:bg-red-700 border-red-600 hover:border-red-700"
                >
                  <RefreshCw size={16} className="mr-2" />
                  Renew Now to Prevent Deletion
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => navigate('/dashboard/services')}
                >
                  Back to Services
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      {service.status !== 'provisioning' && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
          <div className="border-b border-gray-200 bg-gray-50">
            <nav className="flex space-x-1 p-1 overflow-x-auto scrollbar-hide">
                {/* Access Tab - Always visible */}
                <button
                  onClick={() => setActiveTab('access')}
                  className={`
                    flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 whitespace-nowrap flex-shrink-0
                    ${activeTab === 'access'
                      ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                    }
                  `}
                  aria-current={activeTab === 'access' ? 'page' : undefined}
                >
                  <Link2 size={16} className="mr-2 flex-shrink-0" />
                  <span>Access</span>
                </button>

                {/* Monitor Tab - Feature flag controlled */}
                {isFeatureEnabled(FeatureFlags.TAB_SERVICE_MONITOR) && (
                  <button
                    onClick={() => setActiveTab('monitor')}
                    className={`
                      flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 whitespace-nowrap flex-shrink-0
                      ${activeTab === 'monitor'
                        ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }
                    `}
                    aria-current={activeTab === 'monitor' ? 'page' : undefined}
                  >
                    <Activity size={16} className="mr-2 flex-shrink-0" />
                    <span>Monitor</span>
                  </button>
                )}

                {/* Logs Tab - Feature flag controlled */}
                {isFeatureEnabled(FeatureFlags.TAB_SERVICE_LOGS) && (
                  <button
                    onClick={() => setActiveTab('logs')}
                    className={`
                      flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 whitespace-nowrap flex-shrink-0
                      ${activeTab === 'logs'
                        ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }
                    `}
                    aria-current={activeTab === 'logs' ? 'page' : undefined}
                  >
                    <Terminal size={16} className="mr-2 flex-shrink-0" />
                    <span>Logs</span>
                  </button>
                )}

                {/* Upgrade Tab - Feature flag controlled */}
                {isFeatureEnabled(FeatureFlags.TAB_SERVICE_UPGRADE) && (
                  <button
                    onClick={() => setActiveTab('upgrade')}
                    className={`
                      flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 whitespace-nowrap flex-shrink-0
                      ${activeTab === 'upgrade'
                        ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }
                    `}
                    aria-current={activeTab === 'upgrade' ? 'page' : undefined}
                  >
                    <TrendingUp size={16} className="mr-2 flex-shrink-0" />
                    <span>Upgrade & Renew</span>
                  </button>
                )}

                {/* Configuration Tab - Always visible */}
                <button
                  onClick={() => setActiveTab('config')}
                  className={`
                    flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 whitespace-nowrap flex-shrink-0
                    ${activeTab === 'config'
                      ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                    }
                  `}
                  aria-current={activeTab === 'config' ? 'page' : undefined}
                >
                  <Settings size={16} className="mr-2 flex-shrink-0" />
                  <span>Configuration</span>
                </button>
              </nav>
          </div>

          <div className="p-6">
            {/* Access Tab */}
            {activeTab === 'access' && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Access</h2>
                    <p className="text-sm text-gray-500 mt-1">Connect to your service using these endpoints</p>
                  </div>
                </div>

                {/* Access points grid */}
                {(service.public_url || service.admin_url || service.api_url || service.database_url) ? (
                  <div className="grid grid-cols-1 gap-4">
                    {/* Public URL */}
                    {service.public_url && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                              <ExternalLink size={16} className="text-blue-600" />
                            </div>
                            <div className="font-medium">Public URL</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy URL"
                              onClick={() => navigator.clipboard.writeText(service.public_url || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.public_url}
                            </div>
                            <Button
                              as="a"
                              href={service.public_url}
                              target="_blank"
                              variant="primary"
                              size="sm"
                              className="ml-3 flex-shrink-0"
                            >
                              Open
                            </Button>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            The main URL for accessing your service. This is what your users will see.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Admin Console */}
                    {service.admin_url && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                              <Settings size={16} className="text-purple-600" />
                            </div>
                            <div className="font-medium">Admin Console</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy URL"
                              onClick={() => navigator.clipboard.writeText(service.admin_url || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.admin_url}
                            </div>
                            <Button
                              as="a"
                              href={service.admin_url}
                              target="_blank"
                              variant="primary"
                              size="sm"
                              className="ml-3 flex-shrink-0"
                            >
                              Open
                            </Button>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Administrative interface for managing your service settings and data.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* API Documentation */}
                    {service.api_url && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                              <FileText size={16} className="text-green-600" />
                            </div>
                            <div className="font-medium">API Documentation</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy URL"
                              onClick={() => navigator.clipboard.writeText(service.api_url || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.api_url}
                            </div>
                            <Button
                              as="a"
                              href={service.api_url}
                              target="_blank"
                              variant="primary"
                              size="sm"
                              className="ml-3 flex-shrink-0"
                            >
                              Open
                            </Button>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Documentation for the API endpoints available for your service.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Database Console */}
                    {service.database_url && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                              <Database size={16} className="text-yellow-600" />
                            </div>
                            <div className="font-medium">Database Console</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy URL"
                              onClick={() => navigator.clipboard.writeText(service.database_url || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.database_url}
                            </div>
                            <Button
                              as="a"
                              href={service.database_url}
                              target="_blank"
                              variant="primary"
                              size="sm"
                              className="ml-3 flex-shrink-0"
                            >
                              Open
                            </Button>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Direct access to your service's database for advanced management.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* API Key */}
                    {service.api_key && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                              <Key size={16} className="text-indigo-600" />
                            </div>
                            <div className="font-medium">API Key</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy API Key"
                              onClick={() => navigator.clipboard.writeText(service.api_key || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.api_key}
                            </div>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Use this API key to authenticate your requests to the service API.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Username */}
                    {service.admin_username && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                            <div className="font-medium">Username</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy Username"
                              onClick={() => navigator.clipboard.writeText(service.admin_username || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.admin_username}
                            </div>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Username for accessing the admin panel or service dashboard.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Password */}
                    {service.admin_password && (
                      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                            </div>
                            <div className="font-medium">Password</div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              title="Copy Password"
                              onClick={() => navigator.clipboard.writeText(service.admin_password || '')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-mono bg-gray-50 p-2 rounded border border-gray-200 text-gray-600 flex-1 truncate">
                              {service.admin_password}
                            </div>
                          </div>
                          <p className="mt-3 text-sm text-gray-500">
                            Password for accessing the admin panel or service dashboard.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                      <Link2 size={20} className="text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">No access points available</h3>
                    <p className="text-sm text-gray-500 max-w-md mx-auto">
                      Access points will be available once your service is fully provisioned and active.
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Monitor Tab */}
            {activeTab === 'monitor' && isFeatureEnabled(FeatureFlags.TAB_SERVICE_MONITOR) && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Monitor</h2>
                    <p className="text-sm text-gray-500 mt-1">Monitor your service performance and resource usage</p>
                  </div>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleRefresh}
                    isLoading={isRefreshing}
                  >
                    <RefreshCw size={16} className={`mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </div>

                {/* Resource Usage Cards */}
                {(isLoadingMonitorData || isLoadingStorageData) ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="animate-pulse">
                          <div className="flex items-center">
                            <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                            <div className="ml-3 flex-1">
                              <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                              <div className="h-8 bg-gray-200 rounded w-16"></div>
                            </div>
                          </div>
                          <div className="mt-4">
                            <div className="w-full bg-gray-200 rounded-full h-2"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (monitorData || storageData) ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* CPU Usage Card */}
                    {monitorData ? (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Activity size={16} className="text-blue-600" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">CPU Usage</p>
                            <p className="text-2xl font-bold text-gray-900">
                              {monitorService.formatPercentage(monitorData.cpu.percent)}
                            </p>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${Math.min(monitorData.cpu.percent, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="text-center py-8 text-gray-500">
                          <Activity className="h-8 w-8 mx-auto mb-2 opacity-20" />
                          <p className="text-sm">No CPU data</p>
                        </div>
                      </div>
                    )}

                    {/* Memory Card */}
                    {monitorData ? (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <Database size={16} className="text-green-600" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">Memory</p>
                            <p className="text-2xl font-bold text-gray-900">
                              {monitorService.formatPercentage(monitorData.memory.percent)}
                            </p>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-green-600 h-2 rounded-full"
                              style={{ width: `${Math.min(monitorData.memory.percent, 100)}%` }}
                            ></div>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {monitorService.formatBytes(monitorData.memory.usage)} used
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="text-center py-8 text-gray-500">
                          <Database className="h-8 w-8 mx-auto mb-2 opacity-20" />
                          <p className="text-sm">No memory data</p>
                        </div>
                      </div>
                    )}

                    {/* Storage Card */}
                    {storageData ? (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                            <HardDrive size={16} className="text-orange-600" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">Storage Used</p>
                            <p className="text-2xl font-bold text-gray-900">
                              {monitorService.formatBytes(storageData.size)}
                            </p>
                          </div>
                        </div>
                        <div className="mt-4">
                          <p className="text-xs text-gray-500">
                            Total disk usage
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="text-center py-8 text-gray-500">
                          <HardDrive className="h-8 w-8 mx-auto mb-2 opacity-20" />
                          <p className="text-sm">No storage data</p>
                        </div>
                      </div>
                    )}

                    {/* Network Card */}
                    {monitorData ? (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <Activity size={16} className="text-purple-600" />
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">Network</p>
                            <p className="text-2xl font-bold text-gray-900">
                              {monitorService.formatNetworkTraffic(monitorData.network.in + monitorData.network.out)}
                            </p>
                          </div>
                        </div>
                        <div className="mt-4">
                          <div className="grid grid-cols-2 gap-2 text-xs text-gray-500">
                            <div>
                              <span className="text-green-600">↓</span> In: {monitorService.formatNetworkTraffic(monitorData.network.in)}
                            </div>
                            <div>
                              <span className="text-red-600">↑</span> Out: {monitorService.formatNetworkTraffic(monitorData.network.out)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="text-center py-8 text-gray-500">
                          <Activity className="h-8 w-8 mx-auto mb-2 opacity-20" />
                          <p className="text-sm">No network data</p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="text-center py-8 text-gray-500">
                        <Activity className="h-8 w-8 mx-auto mb-2 opacity-20" />
                        <p className="text-sm">No CPU data available</p>
                      </div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="text-center py-8 text-gray-500">
                        <Database className="h-8 w-8 mx-auto mb-2 opacity-20" />
                        <p className="text-sm">No memory data available</p>
                      </div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="text-center py-8 text-gray-500">
                        <HardDrive className="h-8 w-8 mx-auto mb-2 opacity-20" />
                        <p className="text-sm">No storage data available</p>
                      </div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="text-center py-8 text-gray-500">
                        <Activity className="h-8 w-8 mx-auto mb-2 opacity-20" />
                        <p className="text-sm">No network data available</p>
                      </div>
                    </div>
                  </div>
                )}



                {/* Placeholder for charts */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Charts</h3>
                  <div className="text-center py-12 text-gray-500">
                    <Activity className="h-12 w-12 mx-auto mb-4 opacity-20" />
                    <p>Performance charts will be available soon</p>
                    <p className="text-sm mt-1">Real-time monitoring data will be displayed here</p>
                  </div>
                </div>
              </div>
            )}

            {/* Logs Tab */}
            {activeTab === 'logs' && isFeatureEnabled(FeatureFlags.TAB_SERVICE_LOGS) && (
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Service Logs</h2>
                    <p className="text-sm text-gray-500 mt-1">View real-time logs from your service</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handleRefresh}
                      isLoading={isRefreshing || isLoadingLogsData}
                    >
                      <RefreshCw size={16} className="mr-2" />
                      {isLoadingLogsData ? 'Loading...' : 'Refresh Logs'}
                    </Button>
                  </div>
                </div>

                <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-auto max-h-[500px] shadow-inner">
                  <div className="flex justify-between items-center mb-3 text-xs text-gray-400 border-b border-gray-700 pb-2">
                    <span>Service logs</span>
                    {logsData?.data && (
                      <div className="flex gap-2">
                        <button
                          className="hover:text-white transition-colors"
                          onClick={() => {
                            const allLogs = formatLogDataWithHighlighting(logsData.data);
                            const logText = allLogs.map((entry: any) =>
                              `${entry.timestamp ? `[${entry.timestamp}] ` : ''}${entry.level ? `${entry.level.padEnd(5)} ` : ''}${entry.component ? `(${entry.component}) ` : ''}${entry.message}`
                            ).join('\n');

                            const blob = new Blob([logText], { type: 'text/plain' });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `${service.name}-logs-${new Date().toISOString().split('T')[0]}.txt`;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                          }}
                          title="Download logs"
                        >
                          Download
                        </button>
                        <button
                          className="hover:text-white transition-colors"
                          onClick={() => {
                            const allLogs = formatLogDataWithHighlighting(logsData.data);
                            const logText = allLogs.map((entry: any) =>
                              `${entry.timestamp ? `[${entry.timestamp}] ` : ''}${entry.level ? `${entry.level.padEnd(5)} ` : ''}${entry.component ? `(${entry.component}) ` : ''}${entry.message}`
                            ).join('\n');

                            navigator.clipboard.writeText(logText).then(() => {
                              addToast('success', 'Copied', 'Logs copied to clipboard');
                            }).catch(() => {
                              addToast('error', 'Copy Failed', 'Failed to copy logs to clipboard');
                            });
                          }}
                          title="Copy logs to clipboard"
                        >
                          Copy
                        </button>
                      </div>
                    )}
                  </div>

                  {isLoadingLogsData ? (
                    <div className="text-center py-8 text-gray-400">
                      <Loader className="h-8 w-8 mx-auto mb-3 animate-spin opacity-50" />
                      <p>Loading logs...</p>
                    </div>
                  ) : logsData?.data ? (
                    <div className="space-y-1">
                      {formatLogDataWithHighlighting(logsData.data).map((logEntry: any, index: number) => (
                        <div key={index} className="text-xs leading-relaxed font-mono hover:bg-gray-800 px-2 py-1 rounded transition-colors">
                          <div className="flex flex-wrap gap-2 items-start">
                            {logEntry.timestamp && (
                              <span className="text-gray-400 flex-shrink-0 select-all">
                                [{logEntry.timestamp}]
                              </span>
                            )}
                            {logEntry.level && (
                              <span className={`${logEntry.levelColor} font-medium flex-shrink-0 min-w-[50px] select-all`}>
                                {logEntry.level.padEnd(5)}
                              </span>
                            )}
                            {logEntry.component && (
                              <span className="text-cyan-400 flex-shrink-0 select-all">
                                ({logEntry.component})
                              </span>
                            )}
                            <span className="text-gray-100 flex-1 break-all select-all">
                              {logEntry.message}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-400">
                      <Terminal className="h-10 w-10 mx-auto mb-3 opacity-20" />
                      <p>No logs available for this service.</p>
                      <p className="text-xs mt-1">Logs will appear here when your service is active.</p>
                    </div>
                  )}
                </div>
              </div>
            )}


            {/* Upgrade Tab */}
            {activeTab === 'upgrade' && isFeatureEnabled(FeatureFlags.TAB_SERVICE_UPGRADE) && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Upgrade Service</h2>
                    <p className="text-sm text-gray-500 mt-1">Upgrade your service plan or renew subscription</p>
                  </div>
                </div>

                {/* Current Plan */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Current Plan</h3>
                  <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div>
                      <div className="font-medium text-blue-900">{service.service_templates?.name || 'Current Plan'} ({service.plan.charAt(0).toUpperCase() + service.plan.slice(1)})</div>
                      <div className="text-sm text-blue-700">{formatPrice(service.price)}/{service.plan === 'monthly' ? 'month' : service.plan === 'yearly' ? 'year' : service.plan}</div>
                      <div className="text-xs text-blue-600 mt-1">
                        {service.service_templates?.cpu_limit && service.service_templates?.ram_limit && (
                          `${service.service_templates.cpu_limit} CPU • ${service.service_templates.ram_limit}MB RAM`
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-blue-700">Expires</div>
                      <div className="font-medium text-blue-900">{service.expiryFormatted}</div>
                    </div>
                  </div>
                </div>

                {/* Renewal */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Renew Subscription</h3>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 bg-green-50 border border-green-200 rounded-lg gap-4">
                    <div className="flex-1">
                      <div className="font-medium text-green-900">Extend Current Plan</div>
                      <div className="text-sm text-green-700">Renew your {service.service_templates?.name || 'current plan'} for another {service.plan === 'monthly' ? 'month' : service.plan === 'yearly' ? 'year' : service.plan}</div>
                      <div className="text-xs text-green-600 mt-1">Extends expiry date without changing plan</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-green-900">{formatPrice(service.price)}</div>
                      <Button
                        variant="primary"
                        size="sm"
                        className="mt-2"
                        onClick={handleRenewalClick}
                      >
                        Renew
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Upgrade Options */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Available Upgrades</h3>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={fetchAvailableUpgrades}
                      isLoading={isLoadingUpgrades}
                      disabled={isLoadingUpgrades}
                    >
                      <RefreshCw size={16} className={`mr-2 ${isLoadingUpgrades ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>

                  <div className="space-y-4">
                    {isLoadingUpgrades ? (
                      <div className="space-y-4">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="border border-gray-200 rounded-lg p-4">
                            <div className="animate-pulse">
                              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                <div className="flex-1">
                                  <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
                                  <div className="h-4 bg-gray-200 rounded w-2/3 mb-1"></div>
                                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                                </div>
                                <div className="text-right">
                                  <div className="h-5 bg-gray-200 rounded w-20 mb-2"></div>
                                  <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : availableUpgrades.length > 0 ? (
                      availableUpgrades.map((upgrade) => {
                        return (
                          <div key={upgrade.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                              <div className="flex-1">
                                <div className="font-medium text-gray-900">
                                  {upgrade.name}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {upgrade.cpu_limit} CPU • {Math.round(upgrade.ram_limit)}MB RAM • {upgrade.description}
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-medium text-gray-900">
                                  {formatPrice(upgrade.base_price)}/month
                                </div>
                                <div className="text-sm text-gray-500">
                                  Upgrade: {formatPrice(upgrade.upgrade_fee)}
                                </div>
                                <Button
                                  variant="primary"
                                  size="sm"
                                  className="mt-2"
                                  onClick={() => handleUpgradeClick(upgrade)}
                                >
                                  Upgrade
                                </Button>
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-20" />
                        <p>No upgrades available</p>
                        <p className="text-sm mt-1">You're already on the highest available plan or no upgrade options are currently available</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Configuration Tab */}
            {activeTab === 'config' && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">Configuration</h2>
                    <p className="text-sm text-gray-500 mt-1">View and manage your service settings</p>
                  </div>
                </div>

                {/* Service Details Section */}
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="text-md font-medium text-gray-900">Service Details</h3>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Service Type</div>
                        <div className="mt-1 font-medium text-gray-900">{service.service_templates?.name || 'Service'}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</div>
                        <div className="mt-1 font-medium text-gray-900">{service.plan}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Created</div>
                        <div className="mt-1 font-medium text-gray-900">
                          {formatDateForDisplay(service.created_at)}
                        </div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Price</div>
                        <div className="mt-1 font-medium text-gray-900">{formatPrice(service.price)}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Status</div>
                        <div className="mt-1 font-medium text-gray-900 capitalize">{service.status}</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                        <div className="text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</div>
                        <div className="mt-1 font-medium text-gray-900 whitespace-pre-line">{service.expiryFormatted}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Auto Renewal Settings Section */}
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="text-md font-medium text-gray-900">Billing Settings</h3>
                  </div>
                  <div className="p-4">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">Auto Renewal</div>
                        <div className="text-sm text-gray-500 mt-1">Automatically renew this service before it expires</div>
                      </div>
                      <div className="ml-4 flex-shrink-0">
                        <Switch
                          checked={service.is_auto_renewal}
                          onChange={() => handleAutoRenewalToggle(service.is_auto_renewal)}
                          disabled={isUpdatingAutoRenewal}
                          size="md"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Docker Image Section */}
                {isFeatureEnabled(FeatureFlags.TAB_SERVICE_DOCKER_IMAGE) && (
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                    <div>
                      <h3 className="text-md font-medium text-gray-900">Docker Image</h3>
                      <p className="text-sm text-gray-500 mt-1">Configure your service Docker image version</p>
                    </div>
                  </div>

                  {/* Warning Section */}
                  <div className="p-4 bg-yellow-50 border-b border-gray-200">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <AlertTriangle className="h-5 w-5 text-yellow-400" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-yellow-800">⚠️ WARNING - ADVANCED USERS ONLY</h4>
                        <div className="mt-1 text-sm text-yellow-700">
                          <p className="font-semibold">Changing Docker image version will restart your service!</p>
                          <p className="mt-1">Make sure to backup your data before updating. Incorrect versions may cause service failures.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Docker Image Display */}
                  <div className="p-6">
                    <div className="space-y-4">
                      <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm">
                        <div className="flex justify-between items-center mb-3 text-xs text-gray-400 border-b border-gray-700 pb-2">
                          <span>Docker Image Configuration (Read Only)</span>
                        </div>
                        <div className="space-y-2">
                          <div className="flex">
                            <span className="text-red-400 font-medium w-16">Image:</span>
                            <span className="text-blue-400">
                              {isLoadingDockerVersion ? (
                                <span className="flex items-center">
                                  <Loader className="animate-spin h-4 w-4 mr-2" />
                                  Loading...
                                </span>
                              ) : (
                                dockerImageName ||
                                (service.service_templates?.name === 'n8n' ? 'n8nio/n8n' :
                                 service.service_templates?.name === 'WordPress' ? 'wordpress' :
                                 service.service_templates?.name === 'MySQL' ? 'mysql' :
                                 service.service_templates?.name === 'PostgreSQL' ? 'postgres' :
                                 service.service_templates?.name === 'Redis' ? 'redis' :
                                 'nginx')
                              )}
                            </span>
                          </div>
                          <div className="flex">
                            <span className="text-red-400 font-medium w-16">Version:</span>
                            <span className="text-blue-400">
                              {isLoadingDockerVersion ? (
                                <span className="flex items-center">
                                  <Loader className="animate-spin h-4 w-4 mr-2" />
                                  Loading...
                                </span>
                              ) : (
                                selectedDockerVersion
                              )}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-500">
                          {isLoadingDockerVersion ? (
                            <span className="flex items-center">
                              <Loader className="animate-spin h-4 w-4 mr-2" />
                              Loading version...
                            </span>
                          ) : (
                            `Current version: ${selectedDockerVersion}`
                          )}
                        </div>
                        <Button
                          variant="danger"
                          size="sm"
                          onClick={handleEditDockerVersion}
                        >
                          <Settings size={16} className="mr-2" />
                          Edit Version
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                )}

                {/* Environment Variables Section */}
                {isFeatureEnabled(FeatureFlags.TAB_SERVICE_ENV) && (
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                    <div>
                      <h3 className="text-md font-medium text-gray-900">Environment Variables</h3>
                      <p className="text-sm text-gray-500 mt-1">Configure your service environment variables</p>
                    </div>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={fetchEnvironmentData}
                      isLoading={isLoadingEnvironmentData}
                      disabled={isLoadingEnvironmentData}
                    >
                      <RefreshCw size={16} className={`mr-2 ${isLoadingEnvironmentData ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>

                  {/* Warning Section */}
                  <div className="p-4 bg-red-50 border-b border-gray-200">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <AlertCircle className="h-5 w-5 text-red-400" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-red-800">⚠️ WARNING - ADVANCED USERS ONLY</h4>
                        <div className="mt-1 text-sm text-red-700">
                          <p className="font-semibold">Editing environment variables incorrectly can BREAK your application!</p>
                          <p className="mt-1">Only proceed if you understand the impact of your changes. Incorrect values may cause service failures or data loss.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Environment Variables Display */}
                  <div className="p-6">
                    {isLoadingEnvironmentData ? (
                      <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                        <div className="space-y-2">
                          {[1, 2, 3, 4, 5].map((i) => (
                            <div key={i} className="h-3 bg-gray-200 rounded"></div>
                          ))}
                        </div>
                      </div>
                    ) : environmentData?.env ? (
                      <div className="space-y-4">
                        <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-auto max-h-96">
                          <div className="flex justify-between items-center mb-3 text-xs text-gray-400 border-b border-gray-700 pb-2">
                            <span>Environment Variables (Read Only)</span>
                          </div>
                          <div className="space-y-1">
                            {environmentData.env.split('\n').map((line: string, index: number) => {
                              const [key, ...valueParts] = line.split('=');
                              const value = valueParts.join('=');
                              return (
                                <div key={index} className="flex">
                                  <span className="text-red-400 mr-1">{index + 1}</span>
                                  <span className="text-red-400 font-medium">{key}</span>
                                  {value && (
                                    <>
                                      <span className="text-gray-300">=</span>
                                      <span className="text-blue-400">{value}</span>
                                    </>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-500">
                            {environmentData.env.split('\n').length} environment variables configured
                          </div>
                          <Button
                            variant="danger"
                            size="sm"
                            onClick={handleEditEnvVar}
                          >
                            <Settings size={16} className="mr-2" />
                            Edit Variables
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <Settings className="h-12 w-12 mx-auto mb-4 opacity-20" />
                        <p>No environment variables found</p>
                        <p className="text-sm mt-1">Environment variables will appear here once configured</p>
                      </div>
                    )}
                  </div>
                </div>
                )}

                {/* Recent Deployments Section */}
                {isFeatureEnabled(FeatureFlags.TAB_SERVICE_DEPLOYMENT) && (
                <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                    <div>
                      <h3 className="text-md font-medium text-gray-900">Recent Deployments</h3>
                      <p className="text-sm text-gray-500 mt-1">Deployment history for this service</p>
                    </div>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={fetchDeploymentData}
                      isLoading={isLoadingDeploymentData}
                      disabled={isLoadingDeploymentData}
                    >
                      <RefreshCw size={16} className={`mr-2 ${isLoadingDeploymentData ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {isLoadingDeploymentData ? (
                      <div className="space-y-4 p-4">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="animate-pulse">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                                <div>
                                  <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                                  <div className="h-3 bg-gray-200 rounded w-48"></div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="h-4 bg-gray-200 rounded w-16 mb-1"></div>
                                <div className="h-3 bg-gray-200 rounded w-20"></div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : deploymentData.length > 0 ? (
                      deploymentData.map((deployment, index) => {
                        const { icon: StatusIcon, color, bgColor, textColor } = getDeploymentStatusIcon(deployment.status);
                        const { timeAgo, formattedDate } = formatDeploymentDate(deployment.createdAt);

                        return (
                          <div
                            key={index}
                            className="p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                            onClick={() => handleViewDeploymentLogs(
                              `deployment-${index}`,
                              `Deployment`,
                              deployment.status,
                              deployment.log
                            )}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                  <StatusIcon className={`h-5 w-5 ${color}`} />
                                </div>
                                <div>
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm font-medium text-gray-900">
                                      Deployment
                                    </span>
                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
                                      {deployment.status === 'done' ? 'Success' :
                                       deployment.status === 'failed' ? 'Failed' :
                                       deployment.status === 'in_progress' ? 'In Progress' :
                                       deployment.status === 'warning' ? 'Warning' : deployment.status}
                                    </span>
                                  </div>
                                  <div className="text-sm text-gray-500 mt-1">
                                    {deployment.description}
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-sm text-gray-900">{timeAgo}</div>
                                <div className="text-xs text-gray-500">{formattedDate}</div>
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <RefreshCw className="h-12 w-12 mx-auto mb-4 opacity-20" />
                        <p>No deployments found</p>
                        <p className="text-sm mt-1">Deployment history will appear here once you deploy your service</p>
                      </div>
                    )}
                  </div>
                </div>
                )}

                {/* Save and Deploy Button */}
                <div className="flex justify-center pt-6">
                  <Button
                    variant="primary"
                    size="lg"
                    className="px-8"
                    onClick={handleRestart}
                    disabled={isRestarting}
                  >
                    <RefreshCw size={18} className={`mr-2 ${isRestarting ? 'animate-spin' : ''}`} />
                    {isRestarting ? 'Deploying...' : 'Save and Deploy'}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Delete Service Modal */}
      <DeleteServiceModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        serviceName={service.name}
        serviceId={service.id}
        serviceExpiresAt={service.expires_at}
        servicePrice={service.price}
        servicePlan={service.plan}
      />

      {/* Upgrade Confirmation Modal */}
      {upgradeModal.isOpen && service && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Confirm Upgrade</h3>
                <button
                  onClick={() => setUpgradeModal({ isOpen: false })}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              {/* Current vs New Plan */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-700 mb-3">Plan Change</div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">From: {service.service_templates?.name}</span>
                    <span className="text-sm text-gray-600">{formatPrice(service.price)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">To: {upgradeModal.targetPlan}</span>
                    <span className="text-sm font-medium text-gray-900">{formatPrice(upgradeModal.targetPrice || 0)}</span>
                  </div>
                </div>
              </div>



              {/* Billing Information */}
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-sm font-medium text-blue-800 mb-2">Billing Information</div>
                <div className="space-y-1 text-sm text-blue-700">
                  <div className="flex justify-between">
                    <span className="font-medium">Upgrade cost:</span>
                    <span className="font-medium text-blue-900">{formatPrice(upgradeModal.finalCost || 0)}</span>
                  </div>
                </div>
              </div>

              {/* Balance Information */}
              {isLoadingBalance ? (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-sm text-gray-600">Loading balance...</div>
                </div>
              ) : userBalance && userBalance.credits < (upgradeModal.finalCost || 0) ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-sm font-medium text-red-800 mb-2">Insufficient Balance</div>
                  <div className="text-sm text-red-700">
                    You need {formatPrice(upgradeModal.finalCost || 0)} for this upgrade, but only {formatPrice(userBalance.credits)} available. Please top up {formatPrice((upgradeModal.finalCost || 0) - userBalance.credits)} to continue.
                  </div>
                </div>
              ) : null}

              {/* Security Verification */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-800 mb-3">Security Verification</div>
                <div className="flex justify-center">
                  <Turnstile
                    key={turnstileKey}
                    onVerify={handleTurnstileVerify}
                    onError={handleTurnstileError}
                    onExpire={handleTurnstileExpire}
                  />
                </div>
                <p className="text-xs text-gray-600 mt-2 text-center">
                  Please complete the security verification to proceed with upgrade.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3 pt-4">
                <Button
                  variant="secondary"
                  className="flex-1"
                  onClick={() => {
                    setUpgradeModal({ isOpen: false });
                    setTurnstileToken(null);
                    setTurnstileKey(prev => prev + 1);
                  }}
                  disabled={isProcessingUpgrade}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  className="flex-1"
                  onClick={handleServiceUpgrade}
                  isLoading={isProcessingUpgrade}
                  disabled={
                    isProcessingUpgrade ||
                    isLoadingBalance ||
                    !turnstileToken ||
                    !!(userBalance && userBalance.credits < (upgradeModal.finalCost || 0))
                  }
                >
                  Confirm Upgrade
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Renewal Confirmation Modal */}
      {renewalModal.isOpen && service && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Confirm Renewal</h3>
                <button
                  onClick={() => {
                    setRenewalModal({ isOpen: false });
                    setTurnstileToken(null);
                    setTurnstileKey(prev => prev + 1);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-4">
              {/* Current Plan Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-700 mb-2">Renewal Plan</div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{service.service_templates?.name}</div>
                    <div className="text-sm text-gray-600">({service.plan.charAt(0).toUpperCase() + service.plan.slice(1)})</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">{formatPrice(service.price)}</div>
                    <div className="text-xs text-gray-500">per {service.plan === 'monthly' ? 'month' : service.plan === 'yearly' ? 'year' : service.plan}</div>
                  </div>
                </div>
              </div>

              {/* Expiry Date Changes */}
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-sm font-medium text-blue-800 mb-2">Expiry Date Extension</div>
                <div className="space-y-2 text-sm text-blue-700">
                  <div className="flex justify-between">
                    <span>Current expiry:</span>
                    <span className="font-medium">
                      {renewalModal.currentExpiry ? formatDateForDisplay(renewalModal.currentExpiry) : 'Invalid Date'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>New expiry:</span>
                    <span className="font-medium text-blue-900">
                      {renewalModal.newExpiry ? formatDateForDisplay(renewalModal.newExpiry) : 'Invalid Date'}
                    </span>
                  </div>
                  <div className="text-xs text-blue-600 mt-2">
                    * Extends your current plan by 1 {service.plan === 'monthly' ? 'month' : service.plan === 'yearly' ? 'year' : service.plan} from current expiry date
                  </div>
                </div>
              </div>

              {/* Insufficient Balance Warning - Only show when balance is not enough */}
              {userBalance && userBalance.credits < (renewalModal.renewalCost || 0) && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="text-sm font-medium text-red-800 mb-2">Insufficient Balance</div>
                  <div className="text-sm text-red-700">
                    You need {formatPrice(renewalModal.renewalCost || 0)} for this renewal, but only {formatPrice(userBalance.credits)} available. Please top up {formatPrice((renewalModal.renewalCost || 0) - userBalance.credits)} to continue.
                  </div>
                </div>
              )}

              {/* Security Verification */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-800 mb-3">Security Verification</div>
                <div className="flex justify-center">
                  <Turnstile
                    key={turnstileKey}
                    onVerify={handleTurnstileVerify}
                    onError={handleTurnstileError}
                    onExpire={handleTurnstileExpire}
                  />
                </div>
                <p className="text-xs text-gray-600 mt-2 text-center">
                  Please complete the security verification to proceed with renewal.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3 pt-4">
                <Button
                  variant="secondary"
                  className="flex-1"
                  onClick={() => {
                    setRenewalModal({ isOpen: false });
                    setTurnstileToken(null);
                    setTurnstileKey(prev => prev + 1);
                  }}
                  disabled={isProcessingRenewal}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  className="flex-1"
                  onClick={handleServiceRenewal}
                  isLoading={isProcessingRenewal}
                  disabled={
                    isProcessingRenewal ||
                    isLoadingBalance ||
                    !turnstileToken ||
                    !!(userBalance && userBalance.credits < (renewalModal.renewalCost || 0))
                  }
                >
                  {isProcessingRenewal ? 'Processing...' :
                   isLoadingBalance ? 'Checking Balance...' :
                   !turnstileToken ? 'Verifying...' :
                   (userBalance && userBalance.credits < (renewalModal.renewalCost || 0)) ? 'Insufficient Balance' :
                   'Confirm Renewal'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Environment Variables Edit Modal */}
      {envVarModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[95vh] flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Edit Environment Variables</h3>
                <p className="text-sm text-gray-500 mt-1">Configure your service environment variables</p>
              </div>
              <button
                onClick={() => setEnvVarModal({ isOpen: false })}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Critical Warning */}
            <div className="p-4 bg-red-50 border-b border-gray-200 flex-shrink-0">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-6 w-6 text-red-500" />
                </div>
                <div className="ml-3">
                  <h4 className="text-lg font-bold text-red-800">🚨 CRITICAL WARNING 🚨</h4>
                  <div className="mt-2 text-sm text-red-700">
                    <p className="font-semibold">EDITING ENVIRONMENT VARIABLES CAN BREAK YOUR APPLICATION!</p>
                    <ul className="mt-2 list-disc list-inside space-y-1">
                      <li>Incorrect values may cause service failures or data loss</li>
                      <li>Your service will restart automatically after saving changes</li>
                      <li>Only proceed if you understand the impact of your changes</li>
                      <li>Make sure to backup important data before making changes</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Form Content */}
            <form
              onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.currentTarget);
                const envContent = formData.get('envContent') as string;
                handleSaveEnvVar(envContent);
              }}
              className="flex-1 flex flex-col min-h-0"
            >
              {/* Scrollable Content */}
              <div className="flex-1 p-6 overflow-y-auto min-h-0">
                <div className="space-y-4">
                  <div>
                    <label htmlFor="env-content" className="block text-sm font-medium text-gray-700 mb-2">
                      Environment Variables
                    </label>
                    <p className="text-sm text-gray-500 mb-3">
                      Enter one environment variable per line in the format: KEY=VALUE
                    </p>
                    <textarea
                      id="env-content"
                      name="envContent"
                      defaultValue={envVarModal.envContent}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm resize-none"
                      placeholder="WEBHOOK_URL=https://example.com&#10;API_KEY=your-api-key&#10;NODE_ENV=production"
                      rows={15}
                    />
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <Settings className="h-5 w-5 text-blue-400" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-blue-800">Format Guidelines</h4>
                        <div className="mt-1 text-sm text-blue-700">
                          <ul className="list-disc list-inside space-y-1">
                            <li>Use format: KEY=VALUE (no spaces around =)</li>
                            <li>One variable per line</li>
                            <li>No quotes needed around values</li>
                            <li>Empty lines will be ignored</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="border-t border-gray-200 p-6 flex-shrink-0">
                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="secondary"
                    className="flex-1"
                    onClick={() => setEnvVarModal({ isOpen: false })}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    className="flex-1"
                  >
                    <RefreshCw size={16} className="mr-2" />
                    Save
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Environment Update Success Popup */}
      {showEnvUpdateSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">Environment Variables Updated</h3>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-3">
                Your environment variables have been saved successfully.
              </p>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-800">
                      <strong>Important:</strong> To apply the changes, please click the <strong>"Save & Deploy"</strong> button at the bottom of this page.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="secondary"
                className="flex-1"
                onClick={() => setShowEnvUpdateSuccess(false)}
              >
                Close
              </Button>
              <Button
                type="button"
                variant="primary"
                className="flex-1"
                onClick={() => {
                  setShowEnvUpdateSuccess(false);
                  // Scroll to bottom where Save & Deploy button is
                  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                }}
              >
                Ok
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Docker Version Update Success Popup */}
      {showDockerUpdateSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">Docker Version Updated</h3>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-3">
                Your Docker image version has been updated successfully to <strong>{selectedDockerVersion}</strong>.
              </p>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-800">
                      <strong>Important:</strong> To apply the changes, please <strong>restart your service</strong> using the restart button above.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="secondary"
                className="flex-1"
                onClick={() => setShowDockerUpdateSuccess(false)}
              >
                Close
              </Button>
              <Button
                type="button"
                variant="primary"
                className="flex-1"
                onClick={() => {
                  setShowDockerUpdateSuccess(false);
                  // Scroll to top where restart button is
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
              >
                Ok
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Docker Version Edit Modal */}
      {dockerVersionModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[95vh] flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Edit Docker Image Version</h3>
                <p className="text-sm text-gray-500 mt-1">Configure your service Docker image version</p>
              </div>
              <button
                onClick={() => setDockerVersionModal({ isOpen: false })}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.currentTarget);
              const version = formData.get('version') as string;
              handleSaveDockerVersion(version);
            }}>
              {/* Warning Section */}
              <div className="p-4 bg-yellow-50 border-b border-gray-200">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-6 w-6 text-yellow-500" />
                  </div>
                  <div className="ml-3">
                    <h4 className="text-lg font-bold text-yellow-800">🚨 CRITICAL WARNING 🚨</h4>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p className="font-semibold">CHANGING DOCKER IMAGE VERSION WILL RESTART YOUR SERVICE!</p>
                      <ul className="mt-2 list-disc list-inside space-y-1">
                        <li>Your service will be temporarily unavailable during the restart</li>
                        <li>Make sure to backup important data before making changes</li>
                        <li>Incorrect versions may cause service failures</li>
                        <li>Only proceed if you understand the impact of your changes</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Scrollable Content */}
              <div className="flex-1 p-6 overflow-y-auto min-h-0">
                <div className="space-y-4">
                  <div>
                    <label htmlFor="version" className="block text-sm font-medium text-gray-700 mb-2">
                      Docker Image Version
                    </label>
                    <p className="text-sm text-gray-500 mb-3">
                      Enter the Docker image tag/version you want to use (e.g., latest, 1.63.4, 8.0.35)
                    </p>
                    <input
                      id="version"
                      name="version"
                      type="text"
                      defaultValue={dockerVersionModal.version}
                      placeholder="Enter version (e.g., latest, 1.63.4, 8.0.35)"
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
                      required
                    />
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <Database className="h-5 w-5 text-blue-400" />
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-blue-800">Current Configuration</h4>
                        <div className="mt-1 text-sm text-blue-700">
                          <p><strong>Image:</strong> {service.service_templates?.name === 'n8n' ? 'n8nio/n8n' :
                               service.service_templates?.name === 'WordPress' ? 'wordpress' :
                               service.service_templates?.name === 'MySQL' ? 'mysql' :
                               service.service_templates?.name === 'PostgreSQL' ? 'postgres' :
                               service.service_templates?.name === 'Redis' ? 'redis' :
                               'nginx'}</p>
                          <p><strong>Current Version:</strong> {selectedDockerVersion}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="border-t border-gray-200 p-6 flex-shrink-0">
                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="secondary"
                    className="flex-1"
                    onClick={() => setDockerVersionModal({ isOpen: false })}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    className="flex-1"
                  >
                    <RefreshCw size={16} className="mr-2" />
                    Update Version
                  </Button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Deployment Logs Modal */}
      {deploymentLogsModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-medium text-gray-900">{deploymentLogsModal.deploymentName}</h3>
                <p className="text-sm text-gray-500 mt-1">Deployment logs and details</p>
              </div>
              <button
                onClick={() => setDeploymentLogsModal({ isOpen: false })}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="flex-1 overflow-hidden">
              <div className="p-6 space-y-4">
                {/* Deployment Status */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Status</h4>
                      <div className="mt-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          deploymentLogsModal.status === 'done'
                            ? 'bg-green-100 text-green-800'
                            : deploymentLogsModal.status === 'failed'
                            ? 'bg-red-100 text-red-800'
                            : deploymentLogsModal.status === 'in_progress'
                            ? 'bg-blue-100 text-blue-800'
                            : deploymentLogsModal.status === 'warning'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {deploymentLogsModal.status === 'done' ? 'Success' :
                           deploymentLogsModal.status === 'failed' ? 'Failed' :
                           deploymentLogsModal.status === 'in_progress' ? 'In Progress' :
                           deploymentLogsModal.status === 'warning' ? 'Warning' : deploymentLogsModal.status}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">Deployment ID</div>
                      <div className="text-sm font-medium text-gray-900">#{deploymentLogsModal.deploymentId}</div>
                    </div>
                  </div>
                </div>

                {/* Logs Container */}
                <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-auto max-h-96">
                  <div className="flex justify-between items-center mb-3 text-xs text-gray-400 border-b border-gray-700 pb-2">
                    <span>Deployment logs</span>
                    <button className="hover:text-white">
                      Download Logs
                    </button>
                  </div>

                  {/* Deployment logs */}
                  <div className="space-y-1">
                    {deploymentLogsModal.logs ? (
                      <pre className="whitespace-pre-wrap text-gray-300">
                        {deploymentLogsModal.logs}
                      </pre>
                    ) : (
                      <div className="text-gray-400 text-center py-8">
                        <div>No logs available for this deployment</div>
                        <div className="text-xs mt-2">Logs may not be available for older deployments</div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex justify-end">
                <Button
                  variant="secondary"
                  onClick={() => setDeploymentLogsModal({ isOpen: false })}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reset Password Confirmation Modal for n8n services */}
      {resetPasswordModal.isOpen && service && isN8nService && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Reset n8n Password</h3>
                <button
                  onClick={() => setResetPasswordModal({ isOpen: false })}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="mb-6">
                <div className="flex items-start space-x-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <AlertTriangle size={20} className="text-yellow-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm text-yellow-800 font-medium mb-1">
                      This will reset your n8n login credentials
                    </p>
                    <p className="text-sm text-yellow-700">
                      The existing email and password will be removed, but your workflow data will remain intact.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  variant="secondary"
                  onClick={() => setResetPasswordModal({ isOpen: false })}
                  disabled={isProcessingResetPassword}
                >
                  Cancel
                </Button>
                <Button
                  variant="danger"
                  onClick={handleResetPassword}
                  isLoading={isProcessingResetPassword}
                  disabled={isProcessingResetPassword}
                >
                  <Key size={16} className="mr-2" />
                  {isProcessingResetPassword ? 'Resetting...' : 'Reset Password'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceDetails;
