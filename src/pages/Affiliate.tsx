import { useState } from 'react';
import { Copy, Users, DollarSign, Calendar, UserCheck, ShoppingCart, CheckCircle, AlertCircle } from 'lucide-react';
import Button from '../components/common/Button';
import { usePageTitle } from '../hooks/usePageTitle';
import { useAuth } from '../context/AuthContext';
import { useAffiliate } from '../hooks/useAffiliate';

const Affiliate = () => {
  usePageTitle('Affiliate Program');

  const { user } = useAuth();
  const [copied, setCopied] = useState(false);
  
  // Use affiliate hook for real data
  const {
    stats,
    referrals,
    loading,
    error,
    generateReferralLink
  } = useAffiliate(50);

  // Generate referral link
  const referralLink = generateReferralLink();

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  // Show loading state only on initial load
  if (loading && !stats && referrals.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Affiliate Program</h1>
            <p className="mt-1 text-sm text-gray-500">Loading affiliate data...</p>
          </div>
        </div>
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-200 h-24 rounded-lg"></div>
            ))}
          </div>
          <div className="bg-gray-200 h-64 rounded-lg"></div>
        </div>
      </div>
    );
  }

  // If no user, show message
  if (!user) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Affiliate Program</h1>
            <p className="mt-1 text-sm text-gray-500">Please log in to view your affiliate data</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Affiliate Program</h1>
          <p className="mt-1 text-sm text-gray-500">
            Earn 10% commission on confirmed topup payments from your referrals
          </p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-sm font-medium text-red-900">{error}</span>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Referrals</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalReferrals || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserCheck className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Registered</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalRegistered || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ShoppingCart className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Transacted</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalTransacted || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Earnings</p>
              <p className="text-2xl font-bold text-gray-900">
                Rp {stats?.totalEarnings?.toLocaleString('id-ID') || '0'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Referral Link Section */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Your Referral Link</h2>
          <p className="mt-1 text-sm text-gray-500">
            Share this link with your friends and earn 10% commission on confirmed topup payments
          </p>
        </div>
        <div className="px-6 py-5">
          <div className="flex items-center space-x-3">
            <div className="flex-1">
              <input
                type="text"
                value={referralLink}
                readOnly
                className="block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>
            <Button
              onClick={handleCopyLink}
              variant="outline"
              className="flex items-center space-x-2"
            >
              {copied ? (
                <>
                  <CheckCircle size={16} className="text-green-600" />
                  <span>Copied!</span>
                </>
              ) : (
                <>
                  <Copy size={16} />
                  <span>Copy</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">How It Works</h2>
        </div>
        <div className="px-6 py-5">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-blue-600">1</span>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Share Your Link</h3>
                <p className="text-sm text-gray-500">
                  Share your unique referral link with friends, family, or your audience
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-blue-600">2</span>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">They Sign Up</h3>
                <p className="text-sm text-gray-500">
                  When someone clicks your link and creates an account, they become your referral
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-blue-600">3</span>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Earn Commission</h3>
                <p className="text-sm text-gray-500">
                  You earn 10% commission on confirmed topup payments, credited to your balance
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Commission Structure */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Commission Structure</h2>
        </div>
        <div className="px-6 py-5">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-900">
                Earn 10% commission on confirmed topup payments by your referrals
              </span>
            </div>
            <p className="mt-2 text-sm text-blue-700">
              Commission is automatically credited to your balance • Valid for 1 year from registration
            </p>
            <p className="mt-1 text-sm text-blue-600">
              ⚠️ Commission only applies to confirmed payment topups, not vouchers, coupons, or bonus credits
            </p>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-orange-600 mr-2" />
              <span className="text-sm font-medium text-orange-900">
                Affiliate program expires 1 year after user registration
              </span>
            </div>
            <p className="mt-2 text-sm text-orange-700">
              Program can be extended • Contact support for renewal options
            </p>
          </div>
        </div>
      </div>

      {/* Referral History */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Referral Activity</h2>
          <p className="mt-1 text-sm text-gray-500">
            Shows top 50 referral activities and confirmed topup transactions
          </p>
        </div>
        <div className="px-6 py-5">
          {referrals.length === 0 ? (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No referral activity yet</h3>
              <p className="mt-1 text-sm text-gray-500">
                Start sharing your referral link to see registrations and confirmed topup transactions here
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Registration Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Commission
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {referrals.map((referral) => (
                      <tr key={referral.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {referral.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(referral.createdAt).toLocaleDateString('id-ID')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {referral.totalCommission > 0 ? `Rp ${referral.totalCommission.toLocaleString('id-ID')}` : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            referral.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {referral.status === 'active' ? 'Active' : 'Registered'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>


            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Affiliate;
