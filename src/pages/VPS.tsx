import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import { RefreshCw, ExternalLink } from 'lucide-react';
import CreateVPSButton from '../components/common/CreateVPSButton';
import { useVPS } from '../hooks/useVPS';
import { usePageTitle } from '../hooks/usePageTitle';

const VPS = () => {
  usePageTitle('Virtual Private Servers');

  const {
    vps,
    loading,
    error
  } = useVPS();

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Virtual Private Servers</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your virtual private servers
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary" size="sm">
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </Button>
          <CreateVPSButton />
        </div>
      </div>



      {/* VMs table */}
      <div className="bg-white shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Resources
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiry
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center text-gray-500">
                    Loading VPS...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center text-red-500">
                    Error: {error}
                  </td>
                </tr>
              ) : vps.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center text-gray-500">
                    No VPS found matching your criteria
                  </td>
                </tr>
              ) : (
                vps.map((vpsItem) => (
                  <tr key={vpsItem.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link
                        to={`/dashboard/vps/${vpsItem.id}`}
                        className="group flex items-center text-sm font-medium text-gray-900 hover:text-blue-600"
                      >
                        <div>
                          <div className="flex items-center">
                            {vpsItem.name}
                            <ExternalLink size={14} className="ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </div>
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{vpsItem.type || 'Standard'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        vpsItem.status === 'running'
                          ? 'bg-green-100 text-green-800'
                          : vpsItem.status === 'provisioning'
                          ? 'bg-blue-100 text-blue-800'
                          : vpsItem.status === 'stopped'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {vpsItem.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {vpsItem.cpu} CPU, {vpsItem.memory} GB RAM
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatPrice(vpsItem.price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {vpsItem.expiryFormatted}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button
                        as={Link}
                        to={`/dashboard/vps/${vpsItem.id}`}
                        variant="secondary"
                        size="sm"
                      >
                        Manage
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default VPS;
