import { useState } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useVPS } from '../hooks/useVPS';
import Button from '../components/common/Button';
import { usePageTitle } from '../hooks/usePageTitle';
import {
  ArrowLeft,
  ExternalLink,
  RefreshCw,
  Play,
  Square,
  FileText,
  Terminal,
  Settings,
  HardDrive,
  Clock,
  Loader,
  Server,
  Link2,
  Cpu,
  Monitor
} from 'lucide-react';

const VPSDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { vpsItem, loading, error } = useVPS(id);
  const navigate = useNavigate();

  // Set dynamic page title based on VPS name
  usePageTitle(vpsItem ? `${vpsItem.name} - VPS Details` : 'VPS Details');

  // Tab state
  const [activeTab, setActiveTab] = useState<'access' | 'logs' | 'config'>('access');

  const [isRestarting, setIsRestarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isStarting, setIsStarting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle VPS actions
  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  const handleStart = async () => {
    setIsStarting(true);
    // Simulate API call
    setTimeout(() => {
      setIsStarting(false);
    }, 1000);
  };

  const handleStop = async () => {
    setIsStopping(true);
    // Simulate API call
    setTimeout(() => {
      setIsStopping(false);
    }, 1000);
  };

  const handleRestart = async () => {
    setIsRestarting(true);
    // Simulate API call
    setTimeout(() => {
      setIsRestarting(false);
    }, 1000);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader size={24} className="animate-spin text-blue-600 mr-2" />
        <span>Loading VPS details...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
        <strong className="font-bold">Error: </strong>
        <span className="block sm:inline">{error}</span>
      </div>
    );
  }

  if (!vpsItem) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative">
        <strong className="font-bold">Not Found: </strong>
        <span className="block sm:inline">The requested VPS could not be found.</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center">
          <Link to="/dashboard/vps" className="mr-4">
            <Button variant="secondary" size="sm">
              <ArrowLeft size={16} className="mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              {vpsItem.name}
              {vpsItem.status === 'provisioning' && (
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <Loader size={12} className="animate-spin mr-1" />
                  Provisioning
                </span>
              )}
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              {vpsItem.type || 'Standard'} VPS • Created on {new Date(vpsItem.created_at).toLocaleDateString()}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleRefresh}
            isLoading={isRefreshing}
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </Button>
          {vpsItem.status === 'running' ? (
            <>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleStop}
                isLoading={isStopping}
              >
                <Square size={16} className="mr-2" />
                Stop
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleRestart}
                isLoading={isRestarting}
              >
                <RefreshCw size={16} className="mr-2" />
                Restart
              </Button>
            </>
          ) : vpsItem.status === 'stopped' ? (
            <Button
              variant="primary"
              size="sm"
              onClick={handleStart}
              isLoading={isStarting}
            >
              <Play size={16} className="mr-2" />
              Start
            </Button>
          ) : null}
        </div>
      </div>

      {/* VPS Status */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
              <Server size={24} className="text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-medium text-gray-900">VPS Status</h2>
              <div className="mt-1 flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  vpsItem.status === 'running'
                    ? 'bg-green-100 text-green-800'
                    : vpsItem.status === 'provisioning'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {vpsItem.status}
                </span>
                <span className="ml-2 text-sm text-gray-500">
                  {vpsItem.status === 'running'
                    ? 'VPS is running and available'
                    : vpsItem.status === 'provisioning'
                    ? 'VPS is being provisioned'
                    : 'VPS is stopped'}
                </span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Expires</div>
            <div className="text-sm font-medium text-gray-900">{vpsItem.expiryFormatted}</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
        <div className="border-b border-gray-200 bg-gray-50">
          <nav className="flex space-x-1 p-1">
            <button
              className={`
                flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
                ${activeTab === 'access'
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              onClick={() => setActiveTab('access')}
            >
              Access
            </button>
            <button
              className={`
                flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
                ${activeTab === 'logs'
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              onClick={() => setActiveTab('logs')}
            >
              Logs
            </button>
            <button
              className={`
                flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
                ${activeTab === 'config'
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              onClick={() => setActiveTab('config')}
            >
              Configuration
            </button>
          </nav>
        </div>

        {/* Tab content */}
        <div className="p-6">
        {activeTab === 'access' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Access Points</h3>

            {/* Access points grid */}
            {(vpsItem.ssh_url || vpsItem.web_console_url || vpsItem.public_ip) ? (
              <div className="grid grid-cols-1 gap-4">
                {/* SSH Access */}
                {vpsItem.ssh_url && (
                  <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                          <Terminal size={16} className="text-blue-600" />
                        </div>
                        <div className="font-medium">SSH Access</div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          className="text-gray-400 hover:text-gray-500"
                          title="Copy SSH command"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="px-4 py-3">
                      <div className="font-mono text-sm bg-gray-50 p-2 rounded border border-gray-200 overflow-x-auto">
                        {vpsItem.ssh_url}
                      </div>
                    </div>
                  </div>
                )}

                {/* Web Console */}
                {vpsItem.web_console_url && (
                  <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                          <Monitor size={16} className="text-purple-600" />
                        </div>
                        <div className="font-medium">Web Console</div>
                      </div>
                      <div className="flex space-x-2">
                        <a
                          href={vpsItem.web_console_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <ExternalLink size={16} />
                        </a>
                      </div>
                    </div>
                    <div className="px-4 py-3">
                      <div className="font-mono text-sm bg-gray-50 p-2 rounded border border-gray-200 overflow-x-auto">
                        {vpsItem.web_console_url}
                      </div>
                    </div>
                  </div>
                )}

                {/* Public IP */}
                {vpsItem.public_ip && (
                  <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 bg-gray-50">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                          <Link2 size={16} className="text-green-600" />
                        </div>
                        <div className="font-medium">Public IP</div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          className="text-gray-400 hover:text-gray-500"
                          title="Copy IP"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="px-4 py-3">
                      <div className="font-mono text-sm bg-gray-50 p-2 rounded border border-gray-200 overflow-x-auto">
                        {vpsItem.public_ip}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-gray-500">
                No access points available for this VPS.
                {vpsItem.status === 'provisioning' && (
                  <span className="block mt-2">
                    Access points will be available once the VPS is provisioned.
                  </span>
                )}
              </div>
            )}
          </div>
        )}

        {activeTab === 'logs' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">VPS Logs</h3>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
              <pre className="whitespace-pre-wrap">
                {vpsItem.logs?.join('\n') || 'No logs available.'}
              </pre>
            </div>
          </div>
        )}

        {activeTab === 'config' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">VPS Configuration</h3>

            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:p-6">
                <dl className="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                  <div className="sm:col-span-1">
                    <dt className="text-sm font-medium text-gray-500">VPS Type</dt>
                    <dd className="mt-1 text-sm text-gray-900">{vpsItem.type || 'Standard'}</dd>
                  </div>
                  <div className="sm:col-span-1">
                    <dt className="text-sm font-medium text-gray-500">Operating System</dt>
                    <dd className="mt-1 text-sm text-gray-900">{vpsItem.os || 'Linux'}</dd>
                  </div>
                  <div className="sm:col-span-1">
                    <dt className="text-sm font-medium text-gray-500">CPU</dt>
                    <dd className="mt-1 text-sm text-gray-900">{vpsItem.cpu} vCPU</dd>
                  </div>
                  <div className="sm:col-span-1">
                    <dt className="text-sm font-medium text-gray-500">Memory</dt>
                    <dd className="mt-1 text-sm text-gray-900">{vpsItem.memory} GB</dd>
                  </div>
                  <div className="sm:col-span-1">
                    <dt className="text-sm font-medium text-gray-500">Storage</dt>
                    <dd className="mt-1 text-sm text-gray-900">{vpsItem.storage} GB</dd>
                  </div>
                  <div className="sm:col-span-1">
                    <dt className="text-sm font-medium text-gray-500">Region</dt>
                    <dd className="mt-1 text-sm text-gray-900">{vpsItem.region || 'us-east-1'}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
};

export default VPSDetails;
