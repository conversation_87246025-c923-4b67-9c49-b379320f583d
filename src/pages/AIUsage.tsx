import React, { useState, useEffect } from 'react';
import { Activity, Info } from 'lucide-react';
import { usePageTitle } from '../hooks/usePageTitle';
import { formatTokens, fetchExchangeRate, convertUSDToIDRWithRate } from '../utils/currency';
import { useAIUsage } from '../hooks/useAIUsage';

const AIUsagePage = () => {
  usePageTitle('AI Usage');

  const { usage, loading, error } = useAIUsage();
  const [exchangeRate, setExchangeRate] = useState<number | null>(null);

  // Fetch exchange rate when component mounts
  useEffect(() => {
    const loadExchangeRate = async () => {
      try {
        const rate = await fetchExchangeRate();
        setExchangeRate(rate);
      } catch (error) {
        console.error('Error loading exchange rate:', error);
        // Don't set exchange rate on error, keep it null
      }
    };

    loadExchangeRate();
  }, []);

  const formatTimestamp = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  // Tooltip component
  const Tooltip = ({ children, content }: { children: React.ReactNode; content: string }) => (
    <div className="group relative inline-block">
      {children}
      <div className="invisible group-hover:visible absolute z-10 w-64 p-2 mt-1 text-xs bg-gray-900 text-white rounded shadow-md -left-28">
        {content}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-2">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="text-blue-600 hover:text-blue-800"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div>
      {usage.length === 0 ? (
        <div className="text-center py-16">
          <Activity className="mx-auto h-12 w-12 text-gray-300" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No usage data</h3>
          <p className="mt-2 text-gray-500">
            No AI usage found in the last 7 days.
          </p>
          <p className="mt-1 text-sm text-gray-400">
            Start using AI models to see your usage history here.
          </p>
        </div>
      ) : (
        <div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timestamp
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Model
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center space-x-1">
                      <span>Tokens</span>
                      <Tooltip content="Input tokens → Output tokens. Tokens represent text chunks, usually 2-4 characters each.">
                        <Info className="w-4 h-4 text-gray-400 cursor-help" />
                      </Tooltip>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center space-x-1">
                      <span>Cost</span>
                      <Tooltip content="Total cost in USD based on token usage and model pricing. IDR estimation is provided for reference only.">
                        <Info className="w-4 h-4 text-gray-400 cursor-help" />
                      </Tooltip>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center space-x-1">
                      <span>Speed</span>
                      <Tooltip content="Tokens per second (tps). Higher values = faster generation.">
                        <Info className="w-4 h-4 text-gray-400 cursor-help" />
                      </Tooltip>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {usage.map((item) => (
                  <tr key={item.request_id} className="hover:bg-gray-50">
                    {/* Timestamp */}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      {formatTimestamp(item.startTime)}
                    </td>

                    {/* Model */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-xs text-white font-semibold">
                            {item.model.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <span className="text-sm font-medium text-gray-900">
                          {item.model}
                        </span>
                      </div>
                    </td>

                    {/* Tokens */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 font-mono">
                        <span>{formatTokens(item.prompt_tokens)}</span>
                        <span className="text-gray-400 mx-2">→</span>
                        <span>{formatTokens(item.completion_tokens)}</span>
                      </div>
                    </td>

                    {/* Cost */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-mono text-gray-900">
                        $ {item.spend.toFixed(7)}
                      </div>
                      {exchangeRate && (
                        <Tooltip content="This is an estimation based on current exchange rate">
                          <div className="text-xs text-gray-500 cursor-help">
                            (~Rp {convertUSDToIDRWithRate(item.spend, exchangeRate).toFixed(4)})
                          </div>
                        </Tooltip>
                      )}
                    </td>

                    {/* Speed */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-600">
                        {item.total_tokens_per_second.toFixed(1)} tps
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIUsagePage;
