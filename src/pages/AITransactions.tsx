import React from 'react';
import { CreditCard } from 'lucide-react';
import { formatUSD } from '../utils/currency';
import { useAITransactions } from '../hooks/useAITransactions';
import Pagination from '../components/billing/Pagination';

const AITransactionsPage: React.FC = () => {
  const { transactions, loading, pagination } = useAITransactions();



  const getTypeIcon = (type: AITransaction['type']) => {
    switch (type) {
      case 'topup':
        return '↗️';
      case 'usage':
        return '↙️';
      case 'refund':
        return '↩️';
      default:
        return '💰';
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      {transactions.length === 0 ? (
        <div className="text-center py-16">
          <CreditCard className="mx-auto h-12 w-12 text-gray-300" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No transactions found</h3>
          <p className="mt-2 text-gray-500">
            Your transaction history will appear here.
          </p>
        </div>
      ) : (
        <div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {transactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDateTime(transaction.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getTypeIcon(transaction.type)}</span>
                        <span className="capitalize text-gray-900">{transaction.type}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="max-w-xs truncate">
                        {transaction.description}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className={`font-medium ${
                        transaction.type === 'topup' || transaction.type === 'refund'
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {transaction.type === 'topup' || transaction.type === 'refund' ? '+' : '-'}
                        {formatUSD(transaction.amount)}
                      </div>
                      {transaction.metadata?.idrAmount && (
                        <div className="text-xs text-gray-500">
                          Rp {transaction.metadata.idrAmount.toLocaleString('id-ID')}
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination && pagination.totalItems > 0 && (
            <div className="border-t border-gray-200 bg-white px-6 py-4">
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                onPageChange={pagination.setPage}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AITransactionsPage;
