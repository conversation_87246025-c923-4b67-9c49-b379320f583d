import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import Switch from '../components/common/Switch';
import { RefreshCw, ExternalLink, Plus } from 'lucide-react';
import { usePageTitle } from '../hooks/usePageTitle';

const Storage = () => {
  usePageTitle('Managed Storage');

  // Mock data for managed storage buckets
  const buckets = [
    {
      id: 1,
      name: 'production-assets',
      status: 'active',
      storage: 10, // GB total
      storageUsed: 2.5, // GB used
      storageUsage: 25, // percentage
      price: 25000,
      billingPeriod: 'monthly',
      isAutoRenewal: true,
      expiryFormatted: '15/08/2024\n(22 days left)'
    },
    {
      id: 2,
      name: 'backup-storage',
      status: 'active',
      storage: 20, // GB total
      storageUsed: 12.8, // GB used
      storageUsage: 64, // percentage
      price: 45000,
      billingPeriod: 'monthly',
      isAutoRenewal: false,
      expiryFormatted: '20/08/2024\n(27 days left)'
    }
  ];

  const loading = false;
  const error = null;

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Format storage size
  const formatSize = (sizeGB: number) => {
    if (sizeGB < 1) {
      return `${(sizeGB * 1024).toFixed(0)} MB`;
    }
    return `${sizeGB.toFixed(1)} GB`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Managed Storage</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your S3-compatible object storage buckets
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary" size="sm">
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </Button>
          <Button variant="primary" size="sm">
            <Plus size={16} className="mr-2" />
            Create Bucket
          </Button>
        </div>
      </div>

      {/* Storage buckets table */}
      <div className="bg-white shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bucket Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usage
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Auto Renewal
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiry
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-10 text-center text-gray-500">
                    Loading storage buckets...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan={6} className="px-6 py-10 text-center text-red-500">
                    Error: {error}
                  </td>
                </tr>
              ) : buckets.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-10 text-center text-gray-500">
                    No storage buckets found. Create your first bucket to get started.
                  </td>
                </tr>
              ) : (
                buckets.map((bucket) => (
                  <tr key={bucket.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link
                        to={`/dashboard/storage/${bucket.id}`}
                        className="group flex items-center text-sm font-medium text-gray-900 hover:text-blue-600"
                      >
                        <div>
                          <div className="flex items-center">
                            {bucket.name}
                            <ExternalLink size={14} className="ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </div>
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        bucket.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : bucket.status === 'creating'
                          ? 'bg-blue-100 text-blue-800'
                          : bucket.status === 'suspended'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {bucket.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <div className="text-sm text-gray-900 mb-1">
                            {formatSize(bucket.storageUsed)} / {formatSize(bucket.storage)}
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                bucket.storageUsage >= 80
                                  ? 'bg-red-500'
                                  : bucket.storageUsage >= 60
                                  ? 'bg-yellow-500'
                                  : 'bg-green-500'
                              }`}
                              style={{ width: `${bucket.storageUsage}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex justify-center">
                        <Switch
                          checked={bucket.isAutoRenewal}
                          onChange={() => {}}
                          size="sm"
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>
                        <div className="font-medium text-gray-900 capitalize">{bucket.billingPeriod}</div>
                        <div className="text-sm text-gray-500">{formatPrice(bucket.price)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="whitespace-pre-line">{bucket.expiryFormatted}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button
                        as={Link}
                        to={`/dashboard/storage/${bucket.id}`}
                        variant="secondary"
                        size="sm"
                      >
                        Manage
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Storage;
