import { usePageTitle } from '../../hooks/usePageTitle';
import AIHeader from '../../components/ai/AIHeader';
import AIKeys from '../AIKeys';
import AITabs from '../../components/ai/AITabs';
import { useAPIKeys } from '../../hooks/useAPIKeys';

const AIKeysPage = () => {
  usePageTitle('AI API Keys');
  const { refreshData } = useAPIKeys();

  return (
    <div className="space-y-6">
      {/* AI Header with Balance and Actions */}
      <AIHeader onDataRefresh={refreshData} />

      {/* AI Content with Tabs */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        {/* Tabs Navigation */}
        <AITabs />

        {/* Content */}
        <div>
          <AIKeys />
        </div>
      </div>
    </div>
  );
};

export default AIKeysPage;
