import { usePageTitle } from '../../hooks/usePageTitle';
import AIHeader from '../../components/ai/AIHeader';
import AIQuickStart from '../AIQuickStart';
import AITabs from '../../components/ai/AITabs';

const AIQuickStartPage = () => {
  usePageTitle('AI Quick Start');

  return (
    <div className="space-y-6">
      {/* AI Header with Balance and Actions */}
      <AIHeader />

      {/* AI Content with Tabs */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        {/* Tabs Navigation */}
        <AITabs />

        {/* Content */}
        <div>
          <AIQuickStart />
        </div>
      </div>
    </div>
  );
};

export default AIQuickStartPage;
