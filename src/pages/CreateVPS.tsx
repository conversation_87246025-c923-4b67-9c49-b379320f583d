import { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Zap, Server, Monitor, HardDrive, Cpu, MemoryStick, Globe } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import Button from '../components/common/Button';
import { useCreateVPS } from '../hooks/useCreateVPS';
import { usePageTitle } from '../hooks/usePageTitle';

interface VPSConfig {
  name: string;
  os: string;
  resources: {
    cpu: number;
    memory: number;
    storage: number;
  };
  region: string;
  plan: 'monthly' | 'quarterly' | 'biannual' | 'yearly';
}

const CreateVPS = () => {
  usePageTitle('Create VPS');

  const navigate = useNavigate();
  const { createVPS, loading: creatingVPS, error: createError } = useCreateVPS();

  const [config, setConfig] = useState<VPSConfig>({
    name: '',
    os: 'ubuntu-22.04',
    resources: {
      cpu: 1,
      memory: 2,
      storage: 20
    },
    region: 'us-east-1',
    plan: 'monthly'
  });

  const [isDeploying, setIsDeploying] = useState(false);
  const [vpsNameTouched, setVpsNameTouched] = useState(false);

  // Ref for auto-focus
  const vpsNameInputRef = useRef<HTMLInputElement>(null);

  // Auto-focus on VPS name input when component mounts
  useEffect(() => {
    if (vpsNameInputRef.current) {
      vpsNameInputRef.current.focus();
    }
  }, []);

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Available OS options
  const osOptions = [
    { id: 'ubuntu-22.04', name: 'Ubuntu 22.04 LTS', icon: '🐧' },
    { id: 'ubuntu-20.04', name: 'Ubuntu 20.04 LTS', icon: '🐧' },
    { id: 'centos-8', name: 'CentOS 8', icon: '🔴' },
    { id: 'debian-11', name: 'Debian 11', icon: '🌀' },
    { id: 'windows-server-2022', name: 'Windows Server 2022', icon: '🪟' },
    { id: 'windows-server-2019', name: 'Windows Server 2019', icon: '🪟' }
  ];

  // Available regions
  const regionOptions = [
    { id: 'us-east-1', name: 'US East (N. Virginia)', flag: '🇺🇸' },
    { id: 'us-west-2', name: 'US West (Oregon)', flag: '🇺🇸' },
    { id: 'ap-southeast-1', name: 'Asia Pacific (Singapore)', flag: '🇸🇬' },
    { id: 'ap-southeast-3', name: 'Asia Pacific (Jakarta)', flag: '🇮🇩' },
    { id: 'eu-west-1', name: 'Europe (Ireland)', flag: '🇮🇪' }
  ];

  // Calculate costs
  const calculateCosts = () => {
    // Base cost: 50k IDR/month for 1 CPU + 2GB RAM + 20GB storage
    const baseCost = 50000;

    // Additional costs
    const extraCpuCost = Math.max(0, config.resources.cpu - 1) * 25000; // 25k per additional CPU
    const extraMemoryCost = Math.max(0, config.resources.memory - 2) * 15000; // 15k per additional GB RAM
    const extraStorageCost = Math.max(0, config.resources.storage - 20) * 2000; // 2k per additional GB storage

    // Windows OS premium
    const osPremium = config.os.includes('windows') ? 30000 : 0;

    const totalMonthlyCost = baseCost + extraCpuCost + extraMemoryCost + extraStorageCost + osPremium;

    return {
      base: baseCost,
      cpu: extraCpuCost,
      memory: extraMemoryCost,
      storage: extraStorageCost,
      os: osPremium,
      monthly: totalMonthlyCost,
      quarterly: totalMonthlyCost * 3 * 0.9, // 10% discount
      biannual: totalMonthlyCost * 6 * 0.85, // 15% discount
      yearly: totalMonthlyCost * 12 * 0.75 // 25% discount
    };
  };

  const costs = calculateCosts();

  // Validation
  const isVpsNameValid = config.name.trim().length >= 3;
  const showVpsNameError = vpsNameTouched && !isVpsNameValid;

  const handleDeploy = async () => {
    if (!isVpsNameValid) return;

    setIsDeploying(true);

    try {
      const result = await createVPS({
        templateId: 'custom',
        name: config.name,
        plan: config.plan,
        resources: config.resources,
        os: config.os,
        region: config.region
      });

      if (result.error) {
        console.error('VPS creation failed:', result.error);
      } else {
        // Navigate to VPS details page
        navigate(`/dashboard/vps/${result.vpsId}`);
      }
    } catch (error) {
      console.error('VPS creation error:', error);
    } finally {
      setIsDeploying(false);
    }
  };

  return (
    <>
      <div className="space-y-6 pb-20 md:pb-6">
        <div className="flex items-center">
          <Link to="/dashboard/vps" className="mr-4">
            <Button variant="secondary" size="sm">
              <ArrowLeft size={16} className="mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create VPS</h1>
            <p className="mt-1 text-sm text-gray-500">
              Configure your virtual private server
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Configuration Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* VPS Name */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">VPS Configuration</h3>
              <div>
                <label htmlFor="vpsName" className="block text-sm font-medium text-gray-700 mb-2">
                  VPS Name
                </label>
                <input
                  ref={vpsNameInputRef}
                  type="text"
                  id="vpsName"
                  value={config.name}
                  onChange={(e) => setConfig({ ...config, name: e.target.value })}
                  onBlur={() => setVpsNameTouched(true)}
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 ${
                    showVpsNameError
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Enter VPS name (minimum 3 characters)"
                />
                {showVpsNameError && (
                  <p className="mt-1 text-sm text-red-600">VPS name must be at least 3 characters long</p>
                )}
              </div>
            </div>

            {/* Operating System Selection */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Operating System</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {osOptions.map((os) => (
                  <div
                    key={os.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      config.os === os.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setConfig({ ...config, os: os.id })}
                  >
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">{os.icon}</span>
                      <div>
                        <div className="font-medium text-gray-900">{os.name}</div>
                        {os.id.includes('windows') && (
                          <div className="text-xs text-orange-600 mt-1">+Rp 30,000/month</div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Resource Configuration */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Resource Configuration</h3>
              <div className="space-y-6">
                {/* CPU Slider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    CPU Cores (Minimum 1 Core)
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min={1}
                      max={16}
                      step={1}
                      value={config.resources.cpu}
                      onChange={(e) => setConfig({
                        ...config,
                        resources: { ...config.resources, cpu: parseInt(e.target.value) }
                      })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>1 Core</span>
                      <span className="font-medium text-gray-900">{config.resources.cpu} Core{config.resources.cpu !== 1 ? 's' : ''}</span>
                      <span>16 Cores</span>
                    </div>
                  </div>
                </div>

                {/* Memory Slider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Memory (RAM) - Minimum 2 GB
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min={2}
                      max={64}
                      step={2}
                      value={config.resources.memory}
                      onChange={(e) => setConfig({
                        ...config,
                        resources: { ...config.resources, memory: parseInt(e.target.value) }
                      })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>2 GB</span>
                      <span className="font-medium text-gray-900">{config.resources.memory} GB</span>
                      <span>64 GB</span>
                    </div>
                  </div>
                </div>

                {/* Storage Slider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Storage (SSD) - Minimum 20 GB
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min={20}
                      max={1000}
                      step={10}
                      value={config.resources.storage}
                      onChange={(e) => setConfig({
                        ...config,
                        resources: { ...config.resources, storage: parseInt(e.target.value) }
                      })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>20 GB</span>
                      <span className="font-medium text-gray-900">{config.resources.storage} GB</span>
                      <span>1000 GB</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Region Selection */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Region</h3>
              <div className="space-y-3">
                {regionOptions.map((region) => (
                  <div
                    key={region.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      config.region === region.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setConfig({ ...config, region: region.id })}
                  >
                    <div className="flex items-center">
                      <span className="text-xl mr-3">{region.flag}</span>
                      <div className="font-medium text-gray-900">{region.name}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Billing Plan */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Billing Plan</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    config.plan === 'monthly'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setConfig({ ...config, plan: 'monthly' })}
                >
                  <div className="font-medium text-gray-900">Monthly</div>
                  <div className="text-sm text-gray-500">{formatPrice(costs.monthly)}/month</div>
                </div>
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    config.plan === 'quarterly'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setConfig({ ...config, plan: 'quarterly' })}
                >
                  <div className="font-medium text-gray-900">Quarterly</div>
                  <div className="text-sm text-gray-500">{formatPrice(costs.quarterly)}</div>
                  <div className="text-xs text-green-600">Save 10%</div>
                </div>
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    config.plan === 'biannual'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setConfig({ ...config, plan: 'biannual' })}
                >
                  <div className="font-medium text-gray-900">Biannual</div>
                  <div className="text-sm text-gray-500">{formatPrice(costs.biannual)}</div>
                  <div className="text-xs text-green-600">Save 15%</div>
                </div>
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    config.plan === 'yearly'
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setConfig({ ...config, plan: 'yearly' })}
                >
                  <div className="font-medium text-gray-900">Yearly</div>
                  <div className="text-sm text-gray-500">{formatPrice(costs.yearly)}</div>
                  <div className="text-xs text-green-600">Save 25%</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuration Summary</h3>

              <div className="space-y-4">
                <div>
                  <div className="text-sm text-gray-500">VPS Name</div>
                  <div className="font-medium">{config.name || 'Not specified'}</div>
                </div>

                <div>
                  <div className="text-sm text-gray-500">Operating System</div>
                  <div className="font-medium">
                    {osOptions.find(os => os.id === config.os)?.name || 'Ubuntu 22.04 LTS'}
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-500">Resources</div>
                  <div className="text-sm space-y-1">
                    <div>CPU: <span className="font-medium">{config.resources.cpu} core{config.resources.cpu !== 1 ? 's' : ''}</span></div>
                    <div>Memory: <span className="font-medium">{config.resources.memory} GB</span></div>
                    <div>Storage: <span className="font-medium">{config.resources.storage} GB SSD</span></div>
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-500">Region</div>
                  <div className="font-medium">
                    {regionOptions.find(region => region.id === config.region)?.name || 'US East (N. Virginia)'}
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-500">Billing Plan</div>
                  <div className="font-medium capitalize">{config.plan}</div>
                </div>
              </div>

              <div className="border-t border-gray-200 mt-6 pt-6">
                <div className="text-sm text-gray-500 mb-2">Cost Breakdown</div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Base (1 CPU + 2GB RAM + 20GB SSD)</span>
                    <span>{formatPrice(costs.base)}</span>
                  </div>
                  {costs.cpu > 0 && (
                    <div className="flex justify-between">
                      <span>Extra CPU ({config.resources.cpu - 1} cores)</span>
                      <span>{formatPrice(costs.cpu)}</span>
                    </div>
                  )}
                  {costs.memory > 0 && (
                    <div className="flex justify-between">
                      <span>Extra Memory ({config.resources.memory - 2} GB)</span>
                      <span>{formatPrice(costs.memory)}</span>
                    </div>
                  )}
                  {costs.storage > 0 && (
                    <div className="flex justify-between">
                      <span>Extra Storage ({config.resources.storage - 20} GB)</span>
                      <span>{formatPrice(costs.storage)}</span>
                    </div>
                  )}
                  {costs.os > 0 && (
                    <div className="flex justify-between">
                      <span>Windows License</span>
                      <span>{formatPrice(costs.os)}</span>
                    </div>
                  )}
                </div>
                <div className="border-t border-gray-200 mt-4 pt-4">
                  <div className="flex justify-between font-semibold">
                    <span>Total Cost</span>
                    <span className="text-blue-600">
                      {formatPrice(costs[config.plan as keyof typeof costs] as number)}
                      {config.plan === 'monthly' ? '/month' : ''}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-6 hidden md:block">
                <Button
                  variant="primary"
                  className="w-full"
                  onClick={handleDeploy}
                  disabled={isDeploying || !isVpsNameValid}
                >
                  {isDeploying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating VPS...
                    </>
                  ) : (
                    <>
                      <Zap size={18} className="mr-2" />
                      Create VPS
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sticky Bottom Bar */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="text-xs text-gray-500">Estimated Cost</div>
            <div className="text-lg font-semibold text-blue-600">
              {formatPrice(costs[config.plan as keyof typeof costs] as number)}
              {config.plan === 'monthly' ? '/month' : ''}
            </div>
          </div>
          <Button
            variant="primary"
            className="ml-4 px-6"
            onClick={handleDeploy}
            disabled={isDeploying || !isVpsNameValid}
          >
            {isDeploying ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Zap size={18} className="mr-2" />
                Create VPS
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );
};

export default CreateVPS;
