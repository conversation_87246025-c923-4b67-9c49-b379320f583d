import { useState } from 'react';
import { Plus, Wallet, AlertCircle, Ticket } from 'lucide-react';
import Button from '../components/common/Button';
import { usePayments } from '../hooks/usePayments';
import { formatIDR } from '../utils/formatters';
import TopUpModal from '../components/modals/TopUpModal';
import RedeemModal from '../components/modals/RedeemModal.tsx';
import PaymentsList from '../components/billing/PaymentsList';
import BillingTabs from '../components/billing/BillingTabs';
import { usePageTitle } from '../hooks/usePageTitle';

const Payments = () => {
  usePageTitle('Payments');

  const [isTopUpModalOpen, setIsTopUpModalOpen] = useState(false);
  const [isRedeemModalOpen, setIsRedeemModalOpen] = useState(false);

  const {
    userBalance,
    payments,
    loading,
    error,
    paymentPagination
  } = usePayments();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payments</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your payments and view payment history
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => setIsRedeemModalOpen(true)}
          >
            <Ticket size={18} className="mr-2" />
            Redeem
          </Button>
          <Button
            variant="primary"
            onClick={() => setIsTopUpModalOpen(true)}
          >
            <Plus size={18} className="mr-2" />
            Add Credit
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Current Balance */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Wallet className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-gray-500">Current Credits</p>
                {loading.balance ? (
                  <p className="text-2xl font-bold text-gray-900">Loading...</p>
                ) : (
                  <p className="text-2xl font-bold text-gray-900">{userBalance?.credits ? formatIDR(userBalance.credits, 0) : 'Rp 0'}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Billing Content with Tabs */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        {/* Tabs Navigation */}
        <BillingTabs />

        {/* Content */}
        <div>
          <PaymentsList
            payments={payments}
            loading={loading.payments}
            pagination={paymentPagination}
          />
        </div>
      </div>

      {/* TopUp Modal */}
      <TopUpModal
        isOpen={isTopUpModalOpen}
        onClose={() => setIsTopUpModalOpen(false)}
      />

      {/* Redeem Modal */}
      <RedeemModal
        isOpen={isRedeemModalOpen}
        onClose={() => setIsRedeemModalOpen(false)}
      />
    </div>
  );
};

export default Payments;
