import React from 'react';
import { Mail, MessageCircle, HelpCircle } from 'lucide-react';
import { usePageTitle } from '../hooks/usePageTitle';
import Button from '../components/common/Button';

const Support = () => {
  usePageTitle('Support');

  const handleEmailSupport = () => {
    window.location.href = 'mailto:<EMAIL>';
  };

  const handleWhatsAppSupport = () => {
    window.open('https://wa.me/6285190052577', '_blank');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center">
          <HelpCircle className="h-8 w-8 text-blue-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Support</h1>
            <p className="text-gray-600 mt-1">Get help and support for your SumoPod services</p>
          </div>
        </div>
      </div>

      {/* Support Options */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Email Support */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="bg-blue-100 rounded-lg p-3 mr-4">
              <Mail className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Email Support</h3>
              <p className="text-sm text-gray-600">Send us an email for detailed assistance</p>
            </div>
          </div>
          
          <div className="space-y-3 mb-6">
            <div className="text-sm text-gray-600">
              <strong>Email:</strong> <EMAIL>
            </div>
          </div>

          <Button 
            onClick={handleEmailSupport}
            variant="primary"
            className="w-full"
          >
            <Mail className="h-4 w-4 mr-2" />
            Send Email
          </Button>
        </div>

        {/* WhatsApp Support */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="bg-green-100 rounded-lg p-3 mr-4">
              <MessageCircle className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">WhatsApp Support</h3>
              <p className="text-sm text-gray-600">Chat with us directly on WhatsApp</p>
            </div>
          </div>
          
          <div className="space-y-3 mb-6">
            <div className="text-sm text-gray-600">
              <strong>WhatsApp:</strong> +62 851-9005-2577
            </div>
          </div>

          <Button 
            onClick={handleWhatsAppSupport}
            variant="primary"
            className="w-full bg-green-600 hover:bg-green-700"
          >
            <MessageCircle className="h-4 w-4 mr-2" />
            Chat on WhatsApp
          </Button>
        </div>
      </div>

      {/* Additional Information */}
      <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">Before contacting support</h3>
        <div className="space-y-2 text-sm text-blue-800">
          <p>• Please include your service ID or name when reporting issues</p>
          <p>• Describe the problem in detail with steps to reproduce</p>
          <p>• Include any error messages you're seeing</p>
          <p>• Let us know what you were trying to accomplish</p>
        </div>
      </div>
    </div>
  );
};

export default Support;
