import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Edit, Trash2, Key } from 'lucide-react';
import { APIKey } from '../types/ai';
import { aiService } from '../services/aiService';
import { formatUSD } from '../utils/currency';
import CreateAPIKeyModal from '../components/modals/CreateAPIKeyModal';
import EditAPIKeyModal from '../components/modals/EditAPIKeyModal';
import DeleteAPIKeyModal from '../components/modals/DeleteAPIKeyModal';
import ShowAPIKeyModal from '../components/modals/ShowAPIKeyModal';
import { useAPIKeys } from '../hooks/useAPIKeys';

const AIKeysPage: React.FC = () => {
  const navigate = useNavigate();
  const { apiKeys, loading, totalCount, refreshData } = useAPIKeys();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isShowKeyModalOpen, setIsShowKeyModalOpen] = useState(false);
  const [selectedKey, setSelectedKey] = useState<APIKey | null>(null);
  const [newApiKey, setNewApiKey] = useState<string>('');



  const handleEditKey = (key: APIKey) => {
    setSelectedKey(key);
    setIsEditModalOpen(true);
  };

  const handleDeleteKey = (key: APIKey) => {
    setSelectedKey(key);
    setIsDeleteModalOpen(true);
  };

  const handleCreateSubmit = async (name: string, creditLimit?: number) => {
    try {
      const newKey = await aiService.createAPIKey(name, creditLimit);
      setNewApiKey(newKey.key_name);
      setIsShowKeyModalOpen(true);
      setIsCreateModalOpen(false);
      refreshData();
    } catch (error) {
      console.error('Error creating API key:', error);
      // The error will be handled by the CreateAPIKeyModal component
      throw error;
    }
  };

  const handleEditSubmit = async (keyId: string, name: string, creditLimit?: number) => {
    try {
      await aiService.updateAPIKey(keyId, { key_alias: name, max_budget: creditLimit });
      setIsEditModalOpen(false);
      setSelectedKey(null);
      refreshData();
    } catch (error) {
      console.error('Error updating API key:', error);
      // Re-throw the error so the modal can display it
      throw error;
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedKey) return;
    try {
      await aiService.deleteAPIKey(selectedKey.id);
      setIsDeleteModalOpen(false);
      setSelectedKey(null);
      refreshData();
    } catch (error) {
      console.error('Error deleting API key:', error);
    }
  };

  const handleShowKeyClose = () => {
    setIsShowKeyModalOpen(false);
    setNewApiKey('');
    // Refresh the data and navigate to ensure consistent behavior across all pages
    refreshData();
    navigate('/dashboard/ai/keys');
  };

  const maskApiKey = (key: string) => {
    if (key.length <= 8) return key;
    return `${key.substring(0, 8)}...${key.substring(key.length - 4)}`;
  };

  const getRemainingCredits = (key: APIKey) => {
    if (!key.max_budget) return null;
    return key.max_budget - key.spend;
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return '1 day ago';
    return `${diffInDays} days ago`;
  };

  if (loading) {
    return (
      <div className="text-center py-16">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-500">Loading API keys...</p>
      </div>
    );
  }

  return (
    <div className="bg-white">
      {apiKeys.length === 0 ? (
        <div className="text-center py-16">
          <Key className="mx-auto h-12 w-12 text-gray-300" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No API keys found</h3>
          <p className="mt-2 text-gray-500">
            Create your first API key to get started.
          </p>
        </div>
      ) : (
        <div className="bg-white">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    API Key
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Credit Limit
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Spent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {apiKeys.map((key, index) => (
                  <tr key={key.key_name + index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{key.key_alias}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-mono text-gray-600">
                        {maskApiKey(key.key_name)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {key.max_budget ? (
                          <span className="text-green-600">
                            {formatUSD(getRemainingCredits(key)!)} remaining
                          </span>
                        ) : (
                          <span>Unlimited</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-600">
                        {formatUSD(key.spend)} spent
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditKey(key)}
                          className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteKey(key)}
                          className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-white border border-red-300 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>


        </div>
      )}

      {/* Modals */}
      <CreateAPIKeyModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateSubmit}
      />

      <EditAPIKeyModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        apiKey={selectedKey}
        onSubmit={handleEditSubmit}
      />

      <DeleteAPIKeyModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        apiKey={selectedKey}
        onConfirm={handleDeleteConfirm}
      />

      <ShowAPIKeyModal
        isOpen={isShowKeyModalOpen}
        onClose={handleShowKeyClose}
        apiKey={newApiKey}
      />
    </div>
  );
};

export default AIKeysPage;
