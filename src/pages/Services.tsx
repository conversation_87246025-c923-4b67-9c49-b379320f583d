import { useState } from 'react';
import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import Switch from '../components/common/Switch';
import { RefreshCw, ExternalLink } from 'lucide-react';
import CreateServiceButton from '../components/common/CreateServiceButton';
import { useServices } from '../hooks/useServices';
import { usePageTitle } from '../hooks/usePageTitle';
import { useToast } from '../context/ToastContext';



const Services = () => {
  usePageTitle('Services');

  const {
    services,
    loading,
    refreshServices,
    updateAutoRenewal
  } = useServices();

  const { addToast } = useToast();
  const [updatingServices, setUpdatingServices] = useState<Set<string>>(new Set());

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Handle auto renewal toggle
  const handleAutoRenewalToggle = async (serviceId: string, currentValue: boolean) => {
    const newValue = !currentValue;
    setUpdatingServices(prev => new Set(prev).add(serviceId));

    try {
      await updateAutoRenewal(serviceId, newValue);
      addToast('success', 'Auto Renewal Updated', `Auto renewal has been ${newValue ? 'enabled' : 'disabled'} for this service.`);
    } catch (error: any) {
      addToast('error', 'Update Failed', error.message || 'Failed to update auto renewal setting.');
    } finally {
      setUpdatingServices(prev => {
        const newSet = new Set(prev);
        newSet.delete(serviceId);
        return newSet;
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Services</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your managed services
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="secondary"
            size="sm"
            onClick={refreshServices}
            disabled={loading}
          >
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <CreateServiceButton />
        </div>
      </div>





      {/* Services Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Auto Renewal
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiry
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                // Loading skeleton UI
                Array.from({ length: 5 }).map((_, index) => (
                  <tr key={`skeleton-${index}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="h-5 bg-gray-200 rounded-full animate-pulse w-9 mx-auto"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="h-8 bg-gray-200 rounded animate-pulse w-16 ml-auto"></div>
                    </td>
                  </tr>
                ))
              ) : services.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center text-gray-500">
                    No services found matching your criteria
                  </td>
                </tr>
              ) : (
                services.map((service) => (
                  <tr key={service.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link
                        to={`/dashboard/services/${service.id}`}
                        className="group flex items-center text-sm font-medium text-gray-900 hover:text-blue-600"
                      >
                        <div>
                          <div className="flex items-center">
                            {service.name}
                            <ExternalLink size={14} className="ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </div>
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{service.service_templates?.name || 'Unknown'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        service.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : service.status === 'suspended'
                          ? 'bg-yellow-100 text-yellow-800'
                          : service.status === 'stopped'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {service.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>
                        <div className="font-medium text-gray-900 capitalize">{service.plan}</div>
                        <div className="text-sm text-gray-500">{formatPrice(service.price)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex justify-center">
                        <Switch
                          checked={service.is_auto_renewal}
                          onChange={() => handleAutoRenewalToggle(service.id, service.is_auto_renewal)}
                          disabled={updatingServices.has(service.id)}
                          size="sm"
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="whitespace-pre-line">
                        {service.expiryFormatted}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button
                        as={Link}
                        to={`/dashboard/services/${service.id}`}
                        variant="secondary"
                        size="sm"
                      >
                        Manage
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Services;
