import React, { useState } from 'react';
import { Copy, Check, ExternalLink, Zap, Code, Settings, Brain, Key, Activity } from 'lucide-react';
import { usePageTitle } from '../hooks/usePageTitle';

const AIQuickStart = () => {
  usePageTitle('AI Quick Start');
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('curl');

  const baseUrl = 'https://ai.sumopod.com';

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text);
    setCopiedCode(id);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const CodeBlock = ({ code, language, id }: { code: string; language: string; id: string }) => (
    <div className="relative">
      <div className="flex items-center justify-between bg-gray-800 text-white px-4 py-2 rounded-t-lg">
        <span className="text-sm font-medium">{language}</span>
        <button
          onClick={() => copyToClipboard(code, id)}
          className="flex items-center space-x-1 text-gray-300 hover:text-white transition-colors"
        >
          {copiedCode === id ? (
            <>
              <Check size={16} />
              <span className="text-sm">Copied!</span>
            </>
          ) : (
            <>
              <Copy size={16} />
              <span className="text-sm">Copy</span>
            </>
          )}
        </button>
      </div>
      <pre className="bg-gray-900 text-gray-100 p-4 rounded-b-lg overflow-x-auto">
        <code>{code}</code>
      </pre>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">AI API Quick Start</h1>
        <p className="text-lg text-gray-600">
          Get started with SumoPod AI API in minutes. Compatible with OpenAI SDK and tools.
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Zap className="text-blue-600" size={20} />
            <span className="font-medium text-blue-900">Base URL:</span>
            <code className="bg-blue-100 px-2 py-1 rounded text-blue-800 font-mono">
              {baseUrl}
            </code>
            <button
              onClick={() => copyToClipboard(baseUrl, 'base-url')}
              className="text-blue-600 hover:text-blue-800"
            >
              {copiedCode === 'base-url' ? <Check size={16} /> : <Copy size={16} />}
            </button>
          </div>
        </div>
      </div>

      {/* Getting Started */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold text-gray-900 flex items-center space-x-2">
          <Code size={24} />
          <span>Getting Started</span>
        </h2>

        {/* Step 1 */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">1. Authentication</h3>
          <div className="space-y-4">
            <p className="text-gray-600">
              Create an API key from the <a href="/dashboard/ai/keys" className="text-blue-600 hover:text-blue-800 font-medium">API Keys</a> tab.
              Set a budget limit to control your spending.
            </p>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">API Key Format:</h4>
              <code className="text-sm bg-gray-100 px-2 py-1 rounded text-gray-800">
                sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
              </code>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">💡 Pro Tips:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Set budget limits to avoid unexpected charges</li>
                <li>• Use different keys for different projects</li>
                <li>• Monitor usage in the Usage tab</li>
                <li>• Keep your API keys secure and never share them</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Step 2 - Code Examples with Tabs */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">2. Code Examples</h3>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-4">
            <nav className="flex space-x-8">
              {[
                { id: 'curl', name: 'cURL', icon: '🌐' },
                { id: 'python', name: 'Python', icon: '🐍' },
                { id: 'javascript', name: 'JavaScript', icon: '⚡' },
                { id: 'node', name: 'Node.js', icon: '📦' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="space-y-4">
            {activeTab === 'curl' && (
              <CodeBlock
                id="curl-example"
                language="cURL"
                code={`curl ${baseUrl}/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" \\
  -d '{
    "model": "gpt-4o-mini",
    "messages": [
      {
        "role": "user",
        "content": "Say hello in a creative way"
      }
    ],
    "max_tokens": 150,
    "temperature": 0.7
  }'`}
              />
            )}

            {activeTab === 'python' && (
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm text-yellow-800">
                    <strong>Install:</strong> <code className="bg-yellow-100 px-1 rounded">pip install openai</code>
                  </p>
                </div>
                <CodeBlock
                  id="python-example"
                  language="Python"
                  code={`from openai import OpenAI

# Initialize client with SumoPod AI
client = OpenAI(
    api_key="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    base_url="${baseUrl}/v1"
)

# Make a chat completion request
response = client.chat.completions.create(
    model="gpt-4o-mini",
    messages=[
        {"role": "user", "content": "Say hello in a creative way"}
    ],
    max_tokens=150,
    temperature=0.7
)

print(response.choices[0].message.content)`}
                />
              </div>
            )}

            {activeTab === 'javascript' && (
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm text-yellow-800">
                    <strong>Install:</strong> <code className="bg-yellow-100 px-1 rounded">npm install openai</code>
                  </p>
                </div>
                <CodeBlock
                  id="js-example"
                  language="JavaScript"
                  code={`import OpenAI from 'openai';

// Initialize client with SumoPod AI
const openai = new OpenAI({
  apiKey: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  baseURL: '${baseUrl}/v1'
});

// Make a chat completion request
const response = await openai.chat.completions.create({
  model: 'gpt-4o-mini',
  messages: [
    { role: 'user', content: 'Say hello in a creative way' }
  ],
  max_tokens: 150,
  temperature: 0.7
});

console.log(response.choices[0].message.content);`}
                />
              </div>
            )}

            {activeTab === 'node' && (
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-sm text-yellow-800">
                    <strong>Install:</strong> <code className="bg-yellow-100 px-1 rounded">npm install openai</code>
                  </p>
                </div>
                <CodeBlock
                  id="node-example"
                  language="JavaScript"
                  code={`const { OpenAI } = require('openai');

// Initialize client with SumoPod AI
const openai = new OpenAI({
  apiKey: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  baseURL: '${baseUrl}/v1'
});

async function main() {
  try {
    // Make a chat completion request
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'user', content: 'Say hello in a creative way' }
      ],
      max_tokens: 150,
      temperature: 0.7
    });

    console.log(response.choices[0].message.content);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();`}
                />
              </div>
            )}
          </div>
        </div>

        {/* Step 3 - Streaming Example */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">3. Streaming Response (Python)</h3>
          <p className="text-gray-600 mb-4">
            Get real-time streaming responses for better user experience:
          </p>
          <CodeBlock
            id="streaming-example"
            language="Python"
            code={`from openai import OpenAI

client = OpenAI(
    api_key="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    base_url="${baseUrl}/v1"
)

# Stream the response
stream = client.chat.completions.create(
    model="gpt-4o-mini",
    messages=[
        {"role": "user", "content": "Write a short story about AI"}
    ],
    max_tokens=500,
    temperature=0.7,
    stream=True  # Enable streaming
)

# Print each chunk as it arrives
for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")`}
          />
        </div>

        {/* Step 4 - Popular Models */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">4. Popular Models</h3>
          <p className="text-gray-600 mb-4">
            Choose the right model for your use case:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <code className="font-mono text-sm font-medium">gpt-4o-mini</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Fast and cost-effective for most tasks</p>
              <div className="text-xs text-gray-500">
                Best for: Chat, simple tasks, high volume
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <code className="font-mono text-sm font-medium">gpt-4o</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Most capable model for complex tasks</p>
              <div className="text-xs text-gray-500">
                Best for: Complex reasoning, analysis, coding
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <code className="font-mono text-sm font-medium">claude-3-haiku</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Fast Anthropic model for quick responses</p>
              <div className="text-xs text-gray-500">
                Best for: Quick tasks, summarization
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <code className="font-mono text-sm font-medium">deepseek-chat</code>
              </div>
              <p className="text-sm text-gray-600 mb-2">Excellent for coding and technical tasks</p>
              <div className="text-xs text-gray-500">
                Best for: Programming, technical writing
              </div>
            </div>
          </div>

          <div className="mt-4 text-center">
            <a
              href="/dashboard/ai/models"
              className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-800 font-medium"
            >
              <Brain size={16} />
              <span>View all models and pricing</span>
              <ExternalLink size={14} />
            </a>
          </div>
        </div>
      </div>

      {/* n8n Integration */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold text-gray-900 flex items-center space-x-2">
          <Settings size={24} />
          <span>n8n Integration</span>
        </h2>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Using with n8n Workflows</h3>
          <p className="text-gray-600 mb-6">
            Integrate SumoPod AI into your n8n workflows using the OpenAI node with custom configuration:
          </p>

          <div className="space-y-6">
            {/* Step-by-step guide */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
              <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3">1</span>
                Add OpenAI Node
              </h4>
              <p className="text-gray-700 ml-9">Add an OpenAI node to your n8n workflow from the node palette.</p>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
              <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3">2</span>
                Configure Credentials
              </h4>
              <div className="ml-9 space-y-3">
                <p className="text-gray-700">Create new OpenAI credentials with these settings:</p>
                <div className="bg-white border border-gray-300 rounded p-3 space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">API Key:</span>
                    <code className="text-sm bg-gray-100 px-2 py-1 rounded">sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</code>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-gray-700">Base URL:</span>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm bg-blue-100 px-2 py-1 rounded text-blue-800">
                        {baseUrl}/v1
                      </code>
                      <button
                        onClick={() => copyToClipboard(`${baseUrl}/v1`, 'n8n-url')}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {copiedCode === 'n8n-url' ? <Check size={14} /> : <Copy size={14} />}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
              <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3">3</span>
                Select Model
              </h4>
              <p className="text-gray-700 ml-9">
                Choose from available models like <code className="bg-gray-100 px-1 rounded">gpt-4o-mini</code>,
                <code className="bg-gray-100 px-1 rounded ml-1">gpt-4o</code>, or others from the
                <a href="/dashboard/ai/models" className="text-blue-600 hover:text-blue-800 ml-1">Models tab</a>.
              </p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mt-0.5">✓</div>
                <div>
                  <h4 className="font-medium text-green-900 mb-1">Ready to Use!</h4>
                  <p className="text-green-800 text-sm">
                    Your n8n workflow can now use SumoPod AI models with the same OpenAI node interface.
                    Monitor usage and costs in your dashboard.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Resources */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Resources</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a
            href="/dashboard/ai/models"
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
          >
            <Brain size={16} />
            <span>View Available Models</span>
            <ExternalLink size={14} />
          </a>
          <a
            href="/dashboard/ai/keys"
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
          >
            <Key size={16} />
            <span>Manage API Keys</span>
            <ExternalLink size={14} />
          </a>
          <a
            href="/dashboard/ai/usage"
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
          >
            <Activity size={16} />
            <span>Monitor Usage</span>
            <ExternalLink size={14} />
          </a>
        </div>
      </div>
    </div>
  );
};

export default AIQuickStart;
