import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import Button from '../components/common/Button';
import { usePageTitle } from '../hooks/usePageTitle';
import { dummyPods } from '../data/dummyPods';
import {
  ArrowLeft,
  Activity,
  FileText,
  Package,
  Settings,
  Play,
  Square,
  RotateCcw,
  Trash2,
  ExternalLink,
  Copy,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';


const PodDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // Menggunakan dummy data untuk sementara
  const pod = dummyPods.find(p => p.id === parseInt(id || '0')) || null;

  // Set dynamic page title based on pod name
  usePageTitle(pod ? `${pod.name} - Pod Details` : 'Pod Details');

  // Tab state
  const [activeTab, setActiveTab] = useState<'overview' | 'logs' | 'deployments' | 'configurations'>('overview');

  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isRestarting, setIsRestarting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Dummy data for the new sections
  const dummyMetrics = {
    cpu: {
      current: parseFloat(pod?.cpu?.replace('%', '') || '0'),
      limit: 100
    },
    memory: {
      current: pod?.config?.memory ? (pod.config.memory * 1024 * 0.5) : 512,
      limit: pod?.config?.memory ? (pod.config.memory * 1024) : 1024
    },
    disk: { current: 2.1, limit: 10 }
  };

  const dummyDeployments = [
    {
      id: 1,
      version: 'v1.2.3',
      status: 'active',
      deployedAt: '2024-01-15 14:30:00',
      deployedBy: '<EMAIL>'
    },
    {
      id: 2,
      version: 'v1.2.2',
      status: 'inactive',
      deployedAt: '2024-01-10 09:15:00',
      deployedBy: '<EMAIL>'
    },
    {
      id: 3,
      version: 'v1.2.1',
      status: 'inactive',
      deployedAt: '2024-01-05 16:45:00',
      deployedBy: '<EMAIL>'
    }
  ];

  const dummyConfigurations = {
    git: {
      repository: `https://github.com/sumopod/${pod?.name || 'app'}.git`,
      branch: 'main',
      commit: 'abc123def456'
    },
    volumes: [
      { name: `${pod?.name || 'app'}-data`, mountPath: '/app/data', size: '5GB' },
      { name: `${pod?.name || 'app'}-logs`, mountPath: '/app/logs', size: '1GB' },
      { name: `${pod?.name || 'app'}-config`, mountPath: '/app/config', size: '512MB' }
    ],
    environment: [
      { key: 'NODE_ENV', value: 'production' },
      { key: 'PORT', value: pod?.port?.toString() || '3000' },
      { key: 'DATABASE_URL', value: 'postgresql://production-db:5432/app' },
      { key: 'REDIS_URL', value: 'redis://cache-server:6379' },
      { key: 'LOG_LEVEL', value: 'info' }
    ]
  };

  const dummyLogs = pod?.logs || [
    'Application started successfully',
    'Connecting to database...',
    'Loading configuration...',
    'Starting application...'
  ];

  if (!pod) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Pod not found</p>
        <Button as={Link} to="/dashboard/pods" variant="primary" className="mt-4">
          Back to Pods
        </Button>
      </div>
    );
  }

  const handleStart = async () => {
    setIsStarting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsStarting(false);
  };

  const handleStop = async () => {
    setIsStopping(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsStopping(false);
  };

  const handleRestart = async () => {
    setIsRestarting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRestarting(false);
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this pod?')) {
      setIsDeleting(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setIsDeleting(false);
      navigate('/dashboard/pods');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="space-y-6">
      {/* Header with back button, pod name, and key details */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex items-center mb-4 md:mb-0">
            <Link to="/dashboard/pods" className="mr-4">
              <Button variant="secondary" size="sm">
                <ArrowLeft size={16} className="mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900">{pod.name}</h1>
                <span className={`ml-3 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  pod.status === 'running'
                    ? 'bg-green-100 text-green-800'
                    : pod.status === 'stopped'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {pod.status}
                </span>
              </div>
              <p className="mt-1 text-sm text-gray-500">
                {pod.image} • Port {pod.port || 'N/A'}
              </p>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-3">
            {pod.status === 'stopped' ? (
              <Button
                variant="primary"
                size="sm"
                onClick={handleStart}
                disabled={isStarting}
              >
                {isStarting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Starting...
                  </>
                ) : (
                  <>
                    <Play size={16} className="mr-2" />
                    Start
                  </>
                )}
              </Button>
            ) : (
              <>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleRestart}
                  disabled={isRestarting}
                >
                  {isRestarting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                      Restarting...
                    </>
                  ) : (
                    <>
                      <RotateCcw size={16} className="mr-2" />
                      Restart
                    </>
                  )}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={handleStop}
                  disabled={isStopping}
                >
                  {isStopping ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                      Stopping...
                    </>
                  ) : (
                    <>
                      <Square size={16} className="mr-2" />
                      Stop
                    </>
                  )}
                </Button>
              </>
            )}
            <Button
              variant="danger"
              size="sm"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 size={16} className="mr-2" />
                  Delete
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
        <div className="border-b border-gray-200 bg-gray-50">
          <nav className="flex space-x-1 p-1">
            <button
              onClick={() => setActiveTab('overview')}
              className={`
                flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
                ${activeTab === 'overview'
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              aria-current={activeTab === 'overview' ? 'page' : undefined}
            >
              <Activity size={16} className="mr-2 flex-shrink-0" />
              <span>Overview</span>
            </button>
            <button
              onClick={() => setActiveTab('logs')}
              className={`
                flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
                ${activeTab === 'logs'
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              aria-current={activeTab === 'logs' ? 'page' : undefined}
            >
              <FileText size={16} className="mr-2 flex-shrink-0" />
              <span>Logs</span>
            </button>
            <button
              onClick={() => setActiveTab('deployments')}
              className={`
                flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
                ${activeTab === 'deployments'
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              aria-current={activeTab === 'deployments' ? 'page' : undefined}
            >
              <Package size={16} className="mr-2 flex-shrink-0" />
              <span>Deployments</span>
            </button>
            <button
              onClick={() => setActiveTab('configurations')}
              className={`
                flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
                ${activeTab === 'configurations'
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              aria-current={activeTab === 'configurations' ? 'page' : undefined}
            >
              <Settings size={16} className="mr-2 flex-shrink-0" />
              <span>Configurations</span>
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Resource Usage</h3>

              {/* CPU Usage */}
              <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <h4 className="text-md font-medium text-gray-900">CPU Usage</h4>
                </div>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Current Usage</span>
                    <span className="text-sm font-medium text-gray-900">{dummyMetrics.cpu.current}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${dummyMetrics.cpu.current}%` }}
                    ></div>
                  </div>
                  <div className="mt-2 text-xs text-gray-500">
                    Limit: {dummyMetrics.cpu.limit}%
                  </div>
                </div>
              </div>

              {/* Memory Usage */}
              <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <h4 className="text-md font-medium text-gray-900">Memory Usage</h4>
                </div>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Current Usage</span>
                    <span className="text-sm font-medium text-gray-900">{dummyMetrics.memory.current}MB / {dummyMetrics.memory.limit}MB</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${(dummyMetrics.memory.current / dummyMetrics.memory.limit) * 100}%` }}
                    ></div>
                  </div>
                  <div className="mt-2 text-xs text-gray-500">
                    {((dummyMetrics.memory.current / dummyMetrics.memory.limit) * 100).toFixed(1)}% used
                  </div>
                </div>
              </div>

              {/* Disk Usage */}
              <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <h4 className="text-md font-medium text-gray-900">Disk Usage</h4>
                </div>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Current Usage</span>
                    <span className="text-sm font-medium text-gray-900">{dummyMetrics.disk.current}GB / {dummyMetrics.disk.limit}GB</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-600 h-2 rounded-full"
                      style={{ width: `${(dummyMetrics.disk.current / dummyMetrics.disk.limit) * 100}%` }}
                    ></div>
                  </div>
                  <div className="mt-2 text-xs text-gray-500">
                    {((dummyMetrics.disk.current / dummyMetrics.disk.limit) * 100).toFixed(1)}% used
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'logs' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Application Logs</h3>
                <Button variant="secondary" size="sm">
                  <RotateCcw size={16} className="mr-2" />
                  Refresh
                </Button>
              </div>

              <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                <div className="bg-gray-900 text-green-400 font-mono text-sm p-4 max-h-96 overflow-y-auto">
                  {dummyLogs.length > 0 ? (
                    dummyLogs.map((log, index) => (
                      <div key={index} className="mb-1">
                        <span className="text-gray-200">{log}</span>
                      </div>
                    ))
                  ) : (
                    <div className="text-gray-400 italic">No logs available</div>
                  )}
                </div>
              </div>

              <div className="text-sm text-gray-500">
                Showing last 100 lines. Use kubectl or docker logs for full history.
              </div>
            </div>
          )}

          {activeTab === 'deployments' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Deployment History</h3>

              <div className="space-y-4">
                {dummyDeployments.map((deployment) => (
                  <div key={deployment.id} className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                    <div className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-3 ${
                            deployment.status === 'active' ? 'bg-green-500' : 'bg-gray-400'
                          }`}></div>
                          <div>
                            <div className="font-medium text-gray-900">{deployment.version}</div>
                            <div className="text-sm text-gray-500">
                              Deployed by {deployment.deployedBy}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            deployment.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {deployment.status}
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            {deployment.deployedAt}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'configurations' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Pod Configuration</h3>

              {/* Git Source */}
              <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <h4 className="text-md font-medium text-gray-900">Git Source</h4>
                </div>
                <div className="p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Repository</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-900 mr-2">{dummyConfigurations.git.repository}</span>
                      <button
                        onClick={() => copyToClipboard(dummyConfigurations.git.repository)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Branch</span>
                    <span className="text-sm font-medium text-gray-900">{dummyConfigurations.git.branch}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Commit</span>
                    <span className="text-sm font-medium text-gray-900 font-mono">{dummyConfigurations.git.commit}</span>
                  </div>
                </div>
              </div>

              {/* Volumes */}
              <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <h4 className="text-md font-medium text-gray-900">Volumes</h4>
                </div>
                <div className="p-4">
                  <div className="space-y-3">
                    {dummyConfigurations.volumes.map((volume, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-medium text-gray-900">{volume.name}</div>
                          <div className="text-sm text-gray-500">{volume.mountPath}</div>
                        </div>
                        <div className="text-sm font-medium text-gray-900">{volume.size}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Environment Variables */}
              <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <h4 className="text-md font-medium text-gray-900">Environment Variables</h4>
                </div>
                <div className="p-4">
                  <div className="space-y-3">
                    {dummyConfigurations.environment.map((env, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="font-medium text-gray-900 font-mono">{env.key}</div>
                        <div className="flex items-center">
                          <span className="text-sm text-gray-900 mr-2">{env.value}</span>
                          <button
                            onClick={() => copyToClipboard(env.value)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <Copy size={14} />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PodDetails;