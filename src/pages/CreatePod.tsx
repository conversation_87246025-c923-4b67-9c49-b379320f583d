import { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Plus, Minus, Package, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import { usePageTitle } from '../hooks/usePageTitle';

interface PodConfig {
  name: string;
  image: {
    type: 'public' | 'private';
    name: string;
    registry?: {
      username: string;
      password: string;
    };
  };
  resources: {
    cpu: number;
    memory: number;
  };
  network: {
    containerPort: number;
  };
  volumes: Array<{
    name: string;
    mountPath: string;
    size: string; // Changed back to string for MB/GB units
  }>;
  advanced: {
    environmentVariables: Array<{ key: string; value: string }>;
  };
}

const CreatePod = () => {
  usePageTitle('Deploy Pod');

  const [config, setConfig] = useState<PodConfig>({
    name: '',
    image: {
      type: 'public',
      name: 'nginx',
      registry: {
        username: '',
        password: ''
      }
    },
    resources: {
      cpu: 1,
      memory: 1
    },
    network: {
      containerPort: 80
    },
    volumes: [],
    advanced: {
      environmentVariables: []
    }
  });

  const [isDeploying, setIsDeploying] = useState(false);
  const [podNameTouched, setPodNameTouched] = useState(false);

  // Ref for auto-focus
  const podNameInputRef = useRef<HTMLInputElement>(null);

  // Auto-focus on pod name input when component mounts
  useEffect(() => {
    if (podNameInputRef.current) {
      podNameInputRef.current.focus();
    }
  }, []);

  const calculateCost = () => {
    // Base price: 40,000 IDR for 1 core, 1GB RAM
    // Additional: +20,000 IDR per extra core, +20,000 IDR per extra GB RAM
    // Storage: first 1GB free, then 10,000 IDR per additional GB
    const baseCost = 40000; // 1 core, 1GB RAM
    const extraCpuCost = (config.resources.cpu - 1) * 20000;
    const extraMemoryCost = (config.resources.memory - 1) * 20000;

    // Storage cost: first 1GB free, then 10k per additional GB
    const storageCost = config.volumes.reduce((total, volume) => {
      // Convert size to GB for calculation
      let sizeInGB = 0;
      if (volume.size.includes('MB')) {
        sizeInGB = parseInt(volume.size) / 1024;
      } else if (volume.size.includes('GB')) {
        sizeInGB = parseInt(volume.size);
      }
      const extraStorage = Math.max(0, sizeInGB - 1); // First 1GB is free
      return total + (extraStorage * 10000);
    }, 0);

    const totalCost = baseCost + extraCpuCost + extraMemoryCost + storageCost;

    return {
      base: baseCost,
      cpu: extraCpuCost,
      memory: extraMemoryCost,
      storage: storageCost,
      total: totalCost
    };
  };

  // Format price in Indonesian Rupiah
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Validation logic
  const isPodNameValid = config.name.trim().length >= 3;
  const showPodNameError = podNameTouched && !isPodNameValid;

  const handleDeploy = async () => {
    // Mark pod name as touched to show validation errors
    setPodNameTouched(true);

    if (!isPodNameValid) {
      return;
    }

    setIsDeploying(true);

    try {
      console.log('Deploying pod with config:', config);
      // Handle pod deployment here
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate deployment
    } catch (error) {
      console.error('Deployment failed:', error);
    } finally {
      setIsDeploying(false);
    }
  };

  const costs = calculateCost();

  return (
    <>
      <style>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      `}</style>
      <div className="space-y-6 pb-20 md:pb-6">
        {/* Header */}
        <div className="flex items-center">
          <Link to="/dashboard/pods" className="mr-4">
            <Button variant="secondary" size="sm">
              <ArrowLeft size={16} className="mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Deploy Pod</h1>
            <p className="mt-1 text-sm text-gray-500">
              Configure and deploy your containerized application
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Configuration Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Pod Name */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Pod Configuration</h3>
              <div>
                <label htmlFor="podName" className="block text-sm font-medium text-gray-700 mb-2">
                  Pod Name
                </label>
                <input
                  ref={podNameInputRef}
                  type="text"
                  id="podName"
                  value={config.name}
                  onChange={(e) => setConfig({ ...config, name: e.target.value })}
                  onBlur={() => setPodNameTouched(true)}
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 ${
                    showPodNameError
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Enter your pod name (e.g., my-web-app)"
                />
                {showPodNameError && (
                  <p className="mt-2 text-sm text-red-600">
                    Pod name must be at least 3 characters long
                  </p>
                )}
              </div>
            </div>

            {/* Image Configuration */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Container Image</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Image Type</label>
                  <div className="flex space-x-6">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="imageType"
                        value="public"
                        checked={config.image.type === 'public'}
                        onChange={() => setConfig({
                          ...config,
                          image: {
                            ...config.image,
                            type: 'public',
                            registry: { username: '', password: '' }
                          }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">Public Registry</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="imageType"
                        value="private"
                        checked={config.image.type === 'private'}
                        onChange={() => setConfig({
                          ...config,
                          image: { ...config.image, type: 'private' }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <span className="ml-2 text-sm text-gray-700">Private Registry</span>
                    </label>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Image Name
                  </label>
                  <input
                    type="text"
                    value={config.image.name}
                    onChange={(e) => setConfig({
                      ...config,
                      image: { ...config.image, name: e.target.value }
                    })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder={config.image.type === 'public' ? 'nginx:latest' : 'registry.example.com/myapp:latest'}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    {config.image.type === 'public'
                      ? 'Enter a public Docker image (e.g., nginx, node:18, python:3.9)'
                      : 'Enter the full path to your private registry image'
                    }
                  </p>
                </div>

                {config.image.type === 'private' && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Registry Username
                      </label>
                      <input
                        type="text"
                        value={config.image.registry?.username || ''}
                        onChange={(e) => setConfig({
                          ...config,
                          image: {
                            ...config.image,
                            registry: {
                              ...config.image.registry,
                              username: e.target.value,
                              password: config.image.registry?.password || ''
                            }
                          }
                        })}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="username"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Registry Password
                      </label>
                      <input
                        type="password"
                        value={config.image.registry?.password || ''}
                        onChange={(e) => setConfig({
                          ...config,
                          image: {
                            ...config.image,
                            registry: {
                              ...config.image.registry,
                              username: config.image.registry?.username || '',
                              password: e.target.value
                            }
                          }
                        })}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="password"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Resource Configuration */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Resource Configuration</h3>
              <div className="space-y-6">
                {/* CPU Slider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    CPU Cores (Minimum 1 Core)
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min={1}
                      max={4}
                      step={1}
                      value={config.resources.cpu}
                      onChange={(e) => setConfig({
                        ...config,
                        resources: { ...config.resources, cpu: parseInt(e.target.value) }
                      })}
                      className="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>1</span>
                      <span className="font-medium">{config.resources.cpu} Core{config.resources.cpu !== 1 ? 's' : ''}</span>
                      <span>4</span>
                    </div>
                  </div>

                </div>

                {/* Memory Slider */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Memory (Minimum 1 GB)
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min={1}
                      max={8}
                      step={1}
                      value={config.resources.memory}
                      onChange={(e) => setConfig({
                        ...config,
                        resources: { ...config.resources, memory: parseInt(e.target.value) }
                      })}
                      className="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>1 GB</span>
                      <span className="font-medium">{config.resources.memory} GB</span>
                      <span>8 GB</span>
                    </div>
                  </div>

                </div>
              </div>
            </div>

            {/* Network Configuration */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Network Configuration</h3>
              <p className="text-sm text-gray-500 mb-4">Configure the port that your application will expose.</p>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Container Port
                </label>
                <input
                  type="number"
                  value={config.network.containerPort}
                  onChange={(e) => setConfig({
                    ...config,
                    network: { ...config.network, containerPort: parseInt(e.target.value) || 80 }
                  })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="80"
                  min="1"
                  max="65535"
                />
                <p className="mt-1 text-xs text-gray-500">
                  The port number that your application listens on (e.g., 80 for HTTP, 3000 for Node.js)
                </p>
              </div>
            </div>

            {/* Volume Configuration */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Volume Mounts</h3>
                <span className="text-sm text-gray-500">Optional</span>
              </div>
              <p className="text-sm text-gray-500 mb-4">Add persistent storage volumes to your pod. First 1GB is free, then Rp 10,000 per additional GB/month.</p>

              <div className="space-y-4">
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Volumes
                  </label>
                  <button
                    type="button"
                    onClick={() => {
                      const newVolume = { name: '', mountPath: '', size: '128MB' };
                      setConfig({
                        ...config,
                        volumes: [...config.volumes, newVolume]
                      });
                    }}
                    className="text-sm text-blue-600 hover:text-blue-500 font-medium"
                  >
                    <Plus size={16} className="inline mr-1" />
                    Add Volume
                  </button>
                </div>
                {config.volumes.length === 0 ? (
                  <p className="text-sm text-gray-500 italic">No volumes configured</p>
                ) : (
                  <div className="space-y-3">
                    {config.volumes.map((volume, index) => (
                      <div key={index} className="border border-gray-200 rounded-md p-3">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <input
                            type="text"
                            placeholder="Volume name"
                            value={volume.name}
                            onChange={(e) => {
                              const newVolumes = [...config.volumes];
                              newVolumes[index].name = e.target.value;
                              setConfig({
                                ...config,
                                volumes: newVolumes
                              });
                            }}
                            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                          />
                          <input
                            type="text"
                            placeholder="Mount path (e.g., /data)"
                            value={volume.mountPath}
                            onChange={(e) => {
                              const newVolumes = [...config.volumes];
                              newVolumes[index].mountPath = e.target.value;
                              setConfig({
                                ...config,
                                volumes: newVolumes
                              });
                            }}
                            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                          />
                          <div className="flex items-center space-x-2">
                            <select
                              value={volume.size}
                              onChange={(e) => {
                                const newVolumes = [...config.volumes];
                                newVolumes[index].size = e.target.value;
                                setConfig({
                                  ...config,
                                  volumes: newVolumes
                                });
                              }}
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                              <option value="128MB">128 MB</option>
                              <option value="256MB">256 MB</option>
                              <option value="512MB">512 MB</option>
                              <option value="1GB">1 GB</option>
                              <option value="2GB">2 GB</option>
                              <option value="3GB">3 GB</option>
                              <option value="4GB">4 GB</option>
                              <option value="5GB">5 GB</option>
                              <option value="6GB">6 GB</option>
                              <option value="7GB">7 GB</option>
                              <option value="8GB">8 GB</option>
                              <option value="9GB">9 GB</option>
                              <option value="10GB">10 GB</option>
                            </select>
                            <button
                              type="button"
                              onClick={() => {
                                const newVolumes = config.volumes.filter((_, i) => i !== index);
                                setConfig({
                                  ...config,
                                  volumes: newVolumes
                                });
                              }}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Minus size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Advanced Configuration */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Advanced Configuration</h3>
                <span className="text-sm text-gray-500">Optional</span>
              </div>

              <div className="space-y-4">
                {/* Environment Variables */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Environment Variables
                    </label>
                    <button
                      type="button"
                      onClick={() => {
                        const newEnvVar = { key: '', value: '' };
                        setConfig({
                          ...config,
                          advanced: {
                            ...config.advanced,
                            environmentVariables: [...config.advanced.environmentVariables, newEnvVar]
                          }
                        });
                      }}
                      className="text-sm text-blue-600 hover:text-blue-500 font-medium"
                    >
                      <Plus size={16} className="inline mr-1" />
                      Add
                    </button>
                  </div>
                  {config.advanced.environmentVariables.length === 0 ? (
                    <p className="text-sm text-gray-500 italic">No environment variables configured</p>
                  ) : (
                    <div className="space-y-2">
                      {config.advanced.environmentVariables.map((envVar, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            placeholder="KEY"
                            value={envVar.key}
                            onChange={(e) => {
                              const newEnvVars = [...config.advanced.environmentVariables];
                              newEnvVars[index].key = e.target.value;
                              setConfig({
                                ...config,
                                advanced: { ...config.advanced, environmentVariables: newEnvVars }
                              });
                            }}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                          />
                          <span>=</span>
                          <input
                            type="text"
                            placeholder="VALUE"
                            value={envVar.value}
                            onChange={(e) => {
                              const newEnvVars = [...config.advanced.environmentVariables];
                              newEnvVars[index].value = e.target.value;
                              setConfig({
                                ...config,
                                advanced: { ...config.advanced, environmentVariables: newEnvVars }
                              });
                            }}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              const newEnvVars = config.advanced.environmentVariables.filter((_, i) => i !== index);
                              setConfig({
                                ...config,
                                advanced: { ...config.advanced, environmentVariables: newEnvVars }
                              });
                            }}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Minus size={16} />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Desktop Deployment Summary (Hidden on Mobile) */}
          <div className="hidden lg:block lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Deployment Summary</h3>

              <div className="space-y-4">
                <div>
                  <div className="text-sm text-gray-500">Pod Name</div>
                  <div className="font-medium">{config.name || 'Not specified'}</div>
                </div>

                <div>
                  <div className="text-sm text-gray-500">Container Image</div>
                  <div className="font-medium">{config.image.name}</div>
                  <div className="text-xs text-gray-400 capitalize">{config.image.type} registry</div>
                </div>

                <div>
                  <div className="text-sm text-gray-500">Resources</div>
                  <div className="text-sm space-y-1">
                    <div>CPU: <span className="font-medium">{config.resources.cpu} core{config.resources.cpu !== 1 ? 's' : ''}</span></div>
                    <div>Memory: <span className="font-medium">{config.resources.memory} GB</span></div>
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-500">Network</div>
                  <div className="text-sm">
                    <div>Port: <span className="font-medium">{config.network.containerPort}</span></div>
                  </div>
                </div>

                {config.volumes.length > 0 && (
                  <div>
                    <div className="text-sm text-gray-500">Volumes</div>
                    <div className="text-sm space-y-1">
                      {config.volumes.map((volume, index) => (
                        <div key={index}>
                          {volume.name && volume.mountPath && (
                            <div>{volume.name}: <span className="font-medium">{volume.mountPath} ({volume.size})</span></div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Estimated Cost</span>
                    <span className="text-lg font-semibold text-blue-600">{formatPrice(costs.total)}/month</span>
                  </div>
                </div>

                <Button
                  variant="primary"
                  className="w-full"
                  onClick={handleDeploy}
                  disabled={isDeploying || !isPodNameValid}
                >
                  {isDeploying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Deploying...
                    </>
                  ) : (
                    <>
                      <Package size={18} className="mr-2" />
                      Deploy Pod
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sticky Bottom Bar */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-50">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="text-xs text-gray-500">Estimated Cost</div>
            <div className="text-lg font-semibold text-blue-600">
              {formatPrice(costs.total)}/month
            </div>
          </div>
          <Button
            variant="primary"
            className="ml-4 px-6"
            onClick={handleDeploy}
            disabled={isDeploying || !isPodNameValid}
          >
            {isDeploying ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Deploying...
              </>
            ) : (
              <>
                <Zap size={18} className="mr-2" />
                Deploy
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  );
};

export default CreatePod;