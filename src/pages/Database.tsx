import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import { RefreshCw, ExternalLink, Plus } from 'lucide-react';
import { usePageTitle } from '../hooks/usePageTitle';

const Database = () => {
  usePageTitle('Managed Databases');

  // Mock data for managed databases
  const databases = [
    {
      id: 1,
      name: 'production-postgres',
      type: 'PostgreSQL',
      version: '15.4',
      status: 'running',
      storage: 100,
      storageUsed: 45, // GB used
      storageUsage: 45, // percentage
      price: 150000,
      expiryFormatted: '15/08/2024\n(22 days left)'
    },
    {
      id: 2,
      name: 'staging-mysql',
      type: 'MySQL',
      version: '8.0',
      status: 'running',
      storage: 50,
      storageUsed: 11.5, // GB used
      storageUsage: 23, // percentage
      price: 75000,
      expiryFormatted: '20/08/2024\n(27 days left)'
    }
  ];

  const loading = false;
  const error = null;

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Managed Databases</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your PostgreSQL, MySQL, and MariaDB databases
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary" size="sm">
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </Button>
          <Button variant="primary" size="sm">
            <Plus size={16} className="mr-2" />
            Create Database
          </Button>
        </div>
      </div>

      {/* Databases table */}
      <div className="bg-white shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usage
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Storage
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiry
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={8} className="px-6 py-10 text-center text-gray-500">
                    Loading databases...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan={8} className="px-6 py-10 text-center text-red-500">
                    Error: {error}
                  </td>
                </tr>
              ) : databases.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-10 text-center text-gray-500">
                    No databases found. Create your first managed database to get started.
                  </td>
                </tr>
              ) : (
                databases.map((database) => (
                  <tr key={database.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link
                        to={`/dashboard/database/${database.id}`}
                        className="group flex items-center text-sm font-medium text-gray-900 hover:text-blue-600"
                      >
                        <div>
                          <div className="flex items-center">
                            {database.name}
                            <ExternalLink size={14} className="ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                          </div>
                        </div>
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-sm text-gray-900">{database.type}</span>
                        <span className="ml-2 text-xs text-gray-500">v{database.version}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        database.status === 'running'
                          ? 'bg-green-100 text-green-800'
                          : database.status === 'provisioning'
                          ? 'bg-blue-100 text-blue-800'
                          : database.status === 'stopped'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {database.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-1">
                          <div className="flex items-center justify-between text-sm text-gray-900 mb-1">
                            <span>{database.storageUsed} GB / {database.storage} GB</span>
                            <span className="text-gray-500">{database.storageUsage}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                database.storageUsage >= 80
                                  ? 'bg-red-500'
                                  : database.storageUsage >= 60
                                  ? 'bg-yellow-500'
                                  : 'bg-green-500'
                              }`}
                              style={{ width: `${database.storageUsage}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {database.storage} GB
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatPrice(database.price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="whitespace-pre-line">{database.expiryFormatted}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button
                        as={Link}
                        to={`/dashboard/database/${database.id}`}
                        variant="secondary"
                        size="sm"
                      >
                        Manage
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Database;
