import React from 'react';
import { NavLink } from 'react-router-dom';
import { X, LayoutDashboard, Settings, CreditCard, Cloud, Server, HelpCircle, Users, Brain, Database, HardDrive } from 'lucide-react';
import Logo from '../common/Logo';
import { isFeatureEnabled, FeatureFlags } from '../../utils/featureFlags';

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

interface NavItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  external?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar }) => {

  // Define all possible navigation items
  const serviceItem: NavItem = { name: 'Services', path: '/dashboard/services', icon: <Cloud size={20} /> };
  const aiItem: NavItem = { name: 'AI', path: '/dashboard/ai', icon: <Brain size={20} /> };
  const podsItem: NavItem = { name: 'Pods', path: '/dashboard/pods', icon: <LayoutDashboard size={20} /> };
  const vpsItem: NavItem = { name: 'VPS', path: '/dashboard/vps', icon: <Server size={20} /> };
  const databaseItem: NavItem = { name: 'Database', path: '/dashboard/database', icon: <Database size={20} /> };
  const storageItem: NavItem = { name: 'Storage', path: '/dashboard/storage', icon: <HardDrive size={20} /> };
  const billingItem: NavItem = { name: 'Billing', path: '/dashboard/billing', icon: <CreditCard size={20} /> };
  const affiliateItem: NavItem = { name: 'Affiliate', path: '/dashboard/affiliate', icon: <Users size={20} /> };
  const settingsItem: NavItem = { name: 'Settings', path: '/dashboard/settings', icon: <Settings size={20} /> };
  const supportItem: NavItem = {
    name: 'Support',
    path: '/dashboard/support',
    icon: <HelpCircle size={20} />
  };

  // Build navigation items in order: Services, AI, Pods, VPS, Database, Storage, Billing, Affiliate, Settings, Support
  let navItems: NavItem[] = [serviceItem];

  // Add AI item if the feature flag is enabled (after Services)
  if (isFeatureEnabled(FeatureFlags.AI_FEATURE)) {
    navItems.push(aiItem);
  }

  // Add Pods item if the feature flag is enabled (after AI)
  if (isFeatureEnabled(FeatureFlags.PODS_FEATURE)) {
    navItems.push(podsItem);
  }

  // Add VPS item if the feature flag is enabled (after Pods)
  if (isFeatureEnabled(FeatureFlags.VPS_FEATURE)) {
    navItems.push(vpsItem);
  }

  // Add Database item if the feature flag is enabled (after VPS)
  if (isFeatureEnabled(FeatureFlags.DATABASE_FEATURE)) {
    navItems.push(databaseItem);
  }

  // Add Storage item if the feature flag is enabled (after Database)
  if (isFeatureEnabled(FeatureFlags.STORAGE_FEATURE)) {
    navItems.push(storageItem);
  }

  // Add billing, affiliate, settings, and support at the end
  navItems.push(billingItem, affiliateItem, settingsItem, supportItem);

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Sidebar */}
      <div
        className={`
          fixed md:sticky top-0 h-full bg-white border-r border-gray-200 flex-shrink-0 w-64
          transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
          z-50 md:z-0
        `}
      >
        <div className="h-full flex flex-col">
          <div className="h-16 flex items-center justify-between px-4 border-b border-gray-200">
            <Logo />
            <button
              onClick={toggleSidebar}
              className="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <X size={20} />
            </button>
          </div>

          <nav className="flex-1 px-2 py-4 overflow-y-auto">
            <ul className="space-y-1">
              {navItems.map((item) => (
                <li key={item.name}>
                  {item.external ? (
                    <a
                      href={item.path}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors text-gray-700 hover:text-gray-900 hover:bg-gray-100"
                    >
                      <span className="mr-3">{item.icon}</span>
                      {item.name}
                    </a>
                  ) : (
                    <NavLink
                      to={item.path}
                      className={({ isActive }) => `
                        flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                        ${isActive
                          ? 'text-blue-700 bg-blue-50'
                          : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                        }
                      `}
                      end={item.path === '/dashboard/pods'}
                    >
                      <span className="mr-3">{item.icon}</span>
                      {item.name}
                    </NavLink>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;