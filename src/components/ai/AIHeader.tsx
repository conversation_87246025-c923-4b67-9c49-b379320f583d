import { useState, useEffect } from 'react';
import { Plus, Wallet, Key } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Button from '../common/Button';
import { aiService } from '../../services/aiService';
import { AIBalance } from '../../types/ai';
import { formatUSD } from '../../utils/currency';
import AITopUpModal from '../modals/AITopUpModal';
import CreateAPIKeyModal from '../modals/CreateAPIKeyModal';
import ShowAPIKeyModal from '../modals/ShowAPIKeyModal';

interface AIHeaderProps {
  onDataRefresh?: () => void;
}

const AIHeader = ({ onDataRefresh }: AIHeaderProps) => {
  const navigate = useNavigate();

  const [balance, setBalance] = useState<AIBalance | null>(null);
  const [loading, setLoading] = useState(true);
  const [isTopUpModalOpen, setIsTopUpModalOpen] = useState(false);
  const [isCreateAPIKeyModalOpen, setIsCreateAPIKeyModalOpen] = useState(false);
  const [isShowAPIKeyModalOpen, setIsShowAPIKeyModalOpen] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string>('');
  const [apiKeyCount, setApiKeyCount] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [balanceData, apiKeysData] = await Promise.all([
          aiService.getAIBalance(),
          aiService.getAPIKeys()
        ]);
        setBalance(balanceData);
        setApiKeyCount(apiKeysData.count);
      } catch (error) {
        console.error('Error fetching AI data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleCreateAPIKey = async (name: string, creditLimit?: number) => {
    try {
      const newKey = await aiService.createAPIKey(name, creditLimit);
      setNewApiKey(newKey.key_name);
      setIsShowAPIKeyModalOpen(true);
      setIsCreateAPIKeyModalOpen(false);
    } catch (error) {
      console.error('Error creating API key:', error);
      // The error will be handled by the CreateAPIKeyModal component
      throw error;
    }
  };

  const handleShowAPIKeyClose = () => {
    setIsShowAPIKeyModalOpen(false);
    setNewApiKey('');
    // Refresh all data after creation
    refreshData();
    // Redirect to the keys list page
    navigate('/dashboard/ai/keys');
  };

  const refreshBalance = async () => {
    try {
      const balanceData = await aiService.getAIBalance();
      setBalance(balanceData);
    } catch (error) {
      console.error('Error refreshing balance:', error);
    }
  };

  const refreshData = async () => {
    try {
      const [balanceData, apiKeysData] = await Promise.all([
        aiService.getAIBalance(),
        aiService.getAPIKeys()
      ]);
      setBalance(balanceData);
      setApiKeyCount(apiKeysData.count);
      // Call parent refresh function if provided
      if (onDataRefresh) {
        onDataRefresh();
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  };

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Models</h1>
          <p className="mt-1 text-sm text-gray-500">
            Access powerful AI models and manage your usage
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => setIsCreateAPIKeyModalOpen(true)}
            disabled={apiKeyCount >= 100}
            title={apiKeyCount >= 100 ? 'Maximum of 100 API keys allowed. Please delete some keys first.' : ''}
          >
            <Key size={18} className="mr-2" />
            Add API Key {apiKeyCount >= 100 && `(${apiKeyCount}/100)`}
          </Button>
          <Button
            variant="primary"
            onClick={() => setIsTopUpModalOpen(true)}
          >
            <Plus size={18} className="mr-2" />
            Add Credit
          </Button>
        </div>
      </div>

      {/* Balance Card */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Wallet className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm text-gray-500">AI Balance</p>
                {loading ? (
                  <p className="text-2xl font-bold text-gray-900">Loading...</p>
                ) : (
                  <p className={`text-2xl font-bold ${balance && balance.balance < 0 ? 'text-red-600' : 'text-gray-900'}`}>
                    {balance ? formatUSD(balance.balance) : '$0.00'}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top Up Modal */}
      <AITopUpModal
        isOpen={isTopUpModalOpen}
        onClose={() => setIsTopUpModalOpen(false)}
        currentBalance={balance?.balance || 0}
        onTopUpSuccess={refreshBalance}
      />

      {/* API Key Modals */}
      <CreateAPIKeyModal
        isOpen={isCreateAPIKeyModalOpen}
        onClose={() => setIsCreateAPIKeyModalOpen(false)}
        onSubmit={handleCreateAPIKey}
      />

      <ShowAPIKeyModal
        isOpen={isShowAPIKeyModalOpen}
        onClose={handleShowAPIKeyClose}
        apiKey={newApiKey}
      />
    </>
  );
};

export default AIHeader;
