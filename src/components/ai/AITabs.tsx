import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Activity, Brain, Key, CreditCard, BookOpen } from 'lucide-react';
import { isFeatureEnabled, FeatureFlags } from '../../utils/featureFlags';

const AITabs: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Base tabs that are always available
  const baseTabs = [
    {
      name: 'Quick Start',
      key: 'quickstart',
      path: '/dashboard/ai/quickstart',
      icon: <BookOpen size={16} />
    },
    {
      name: 'Usage',
      key: 'usage',
      path: '/dashboard/ai/usage',
      icon: <Activity size={16} />
    },
    {
      name: 'Models',
      key: 'models',
      path: '/dashboard/ai/models',
      icon: <Brain size={16} />
    },
    {
      name: 'API Keys',
      key: 'keys',
      path: '/dashboard/ai/keys',
      icon: <Key size={16} />
    }
  ];

  // Conditionally add Transactions tab if feature flag is enabled
  const tabs = [...baseTabs];
  if (isFeatureEnabled(FeatureFlags.AI_TRANSACTION_TAB)) {
    tabs.push({
      name: 'Transactions',
      key: 'transactions',
      path: '/dashboard/ai/transactions',
      icon: <CreditCard size={16} />
    });
  }

  const handleTabClick = (path: string) => {
    navigate(path);
  };

  const isActiveTab = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="border-b border-gray-200 bg-gray-50">
      <nav className="flex space-x-1 p-1">
        {tabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => handleTabClick(tab.path)}
            className={`
              flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
              ${isActiveTab(tab.path)
                ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
              }
            `}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.name}
          </button>
        ))}
      </nav>
    </div>
  );
};

export default AITabs;
