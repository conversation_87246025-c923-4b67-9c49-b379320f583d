import React from 'react';
import { X, Copy } from 'lucide-react';
import Button from '../common/Button';

interface ShowAPIKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  apiKey: string;
}

const ShowAPIKeyModal: React.FC<ShowAPIKeyModalProps> = ({
  isOpen,
  onClose,
  apiKey,
}) => {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(apiKey);
    // You can add a toast notification here
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Create a Key</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Your new key:
            </h3>
            
            <div className="relative">
              <div className="bg-gray-50 border border-gray-200 rounded-md p-3 font-mono text-sm break-all">
                {apiKey}
              </div>
              <button
                onClick={copyToClipboard}
                className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="Copy to clipboard"
              >
                <Copy className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="space-y-3 text-sm text-gray-600">
            <p>
              <strong>Please copy it now and write it down somewhere safe.</strong>{' '}
              <span className="font-medium">You will not be able to see it again.</span>
            </p>
            
            <p>
              You can use it with OpenAI-compatible apps, or{' '}
              <a href="#" className="text-blue-600 hover:text-blue-800">
                your own code
              </a>
            </p>
          </div>

          <div className="flex justify-end">
            <Button
              onClick={onClose}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Done
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShowAPIKeyModal;
