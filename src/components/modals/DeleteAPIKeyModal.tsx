import React from 'react';
import { X, AlertTriangle } from 'lucide-react';
import Button from '../common/Button';
import { APIKey } from '../../types/ai';

interface DeleteAPIKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  apiKey: APIKey | null;
}

const DeleteAPIKeyModal: React.FC<DeleteAPIKeyModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  apiKey,
}) => {
  if (!isOpen || !apiKey) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Delete API Key</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                Are you sure you want to delete this API key?
              </h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>
                  <strong>Name:</strong> {apiKey.key_alias}
                </p>
                <p>
                  <strong>Key:</strong> {apiKey.key_name.substring(0, 8)}...{apiKey.key_name.substring(apiKey.key_name.length - 4)}
                </p>
                <p className="text-red-600 font-medium mt-3">
                  This action cannot be undone. Any applications using this key will stop working immediately.
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button
              type="button"
              onClick={onClose}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={onConfirm}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete API Key
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteAPIKeyModal;
