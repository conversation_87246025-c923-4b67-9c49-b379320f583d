import React, { useState, useEffect } from 'react';
import { X, AlertCircle, DollarSign, ArrowRight } from 'lucide-react';
import Button from '../common/Button';
import { formatUSD, calculateIDRForUSDWithRate, fetchExchangeRate } from '../../utils/currency';
import { formatIDR } from '../../utils/formatters';
import { aiService } from '../../services/aiService';
import { useBilling } from '../../hooks/useBilling';

interface AITopUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentBalance: number;
  onTopUpSuccess: () => void;
}

const AITopUpModal: React.FC<AITopUpModalProps> = ({
  isOpen,
  onClose,
  currentBalance,
  onTopUpSuccess
}) => {
  const [usdAmount, setUsdAmount] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [exchangeRate, setExchangeRate] = useState<number | null>(null);
  const [rateLoading, setRateLoading] = useState(true);
  const [rateError, setRateError] = useState<string>('');

  const { userBalance } = useBilling();

  // Fetch exchange rate when modal opens
  useEffect(() => {
    if (isOpen) {
      const loadExchangeRate = async () => {
        try {
          setRateLoading(true);
          setRateError('');
          const rate = await fetchExchangeRate();
          setExchangeRate(rate);
        } catch (error) {
          console.error('Error loading exchange rate:', error);
          setRateError('Failed to load exchange rate. Please try again.');
          setExchangeRate(null);
        } finally {
          setRateLoading(false);
        }
      };

      loadExchangeRate();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // Only calculate conversion if we have a valid exchange rate
  const conversion = exchangeRate ? calculateIDRForUSDWithRate(usdAmount, exchangeRate) : null;
  const hasEnoughIDR = conversion && userBalance && userBalance.credits >= conversion.idrAmount;

  const handleSubmit = async () => {
    if (usdAmount <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    if (!exchangeRate) {
      setError('Exchange rate not available. Please try again.');
      return;
    }

    if (!hasEnoughIDR) {
      setError('Insufficient IDR balance');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const result = await aiService.topUpAIBalance(usdAmount);

      if (result.success) {
        // Close modal and refresh the entire page
        onClose();
        window.location.reload();
      } else {
        // Show specific error message from API
        const errorMessage = result.message || result.error || 'Top-up failed';
        setError(errorMessage);
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose} />

        <div className="relative w-full max-w-md rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Top Up AI Balance</h3>
            <button
              onClick={onClose}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="space-y-4">
              {/* Current Balance */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Current AI Balance</p>
                    <p className="text-lg font-semibold text-gray-900">{formatUSD(currentBalance)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">IDR Balance</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {userBalance ? formatIDR(userBalance.credits, 0) : 'Loading...'}
                    </p>
                  </div>
                </div>
              </div>

              {/* USD Amount Input */}
              <div>
                <label htmlFor="usd-amount" className="block text-sm font-medium text-gray-700">
                  Amount (USD)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    id="usd-amount"
                    value={usdAmount}
                    onChange={(e) => setUsdAmount(Number(e.target.value))}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="0.00"
                    min="1"
                    step="1"
                  />
                </div>
              </div>

              {/* Quick Amount Buttons */}
              <div className="grid grid-cols-4 gap-2">
                {[5, 10, 25, 50].map((quickAmount) => (
                  <button
                    key={quickAmount}
                    onClick={() => setUsdAmount(quickAmount)}
                    className="rounded-md border border-gray-300 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    ${quickAmount}
                  </button>
                ))}
              </div>

              {/* Conversion Display */}
              {usdAmount > 0 && (
                <div className="bg-blue-50 rounded-lg p-4">
                  {conversion ? (
                    <>
                      <div className="flex items-center justify-between">
                        <div className="text-center">
                          <p className="text-sm text-blue-600">You'll pay</p>
                          <p className="text-lg font-semibold text-blue-900">
                            {formatIDR(conversion.idrAmount, 0)}
                          </p>
                        </div>
                        <ArrowRight className="h-5 w-5 text-blue-600" />
                        <div className="text-center">
                          <p className="text-sm text-blue-600">You'll receive</p>
                          <p className="text-lg font-semibold text-blue-900">
                            {formatUSD(conversion.usdAmount)}
                          </p>
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-blue-600 text-center">
                        {rateLoading ? (
                          'Loading exchange rate...'
                        ) : exchangeRate ? (
                          `Exchange rate: 1 USD = ${exchangeRate.toLocaleString()} IDR`
                        ) : (
                          'Exchange rate unavailable'
                        )}
                      </div>
                    </>
                  ) : (
                    <div className="text-center text-blue-600">
                      {rateLoading ? 'Loading exchange rate...' : 'Exchange rate unavailable'}
                    </div>
                  )}
                </div>
              )}

              {/* Exchange Rate Error */}
              {rateError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                    <div>
                      <p className="text-sm text-red-800 font-medium">Exchange Rate Error</p>
                      <p className="text-xs text-red-600 mt-1">{rateError}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Insufficient Balance Warning */}
              {usdAmount > 0 && conversion && !hasEnoughIDR && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                    <div>
                      <p className="text-sm text-red-800 font-medium">Insufficient IDR Balance</p>
                      <p className="text-xs text-red-600 mt-1">
                        You need {formatIDR(conversion.idrAmount, 0)} but only have{' '}
                        {userBalance ? formatIDR(userBalance.credits, 0) : 'Rp 0'}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="px-6 py-2">
              <div className="flex items-center text-red-600 text-sm">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 border-t border-gray-200 px-6 py-4">
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={loading || rateLoading || usdAmount <= 0 || !exchangeRate || !hasEnoughIDR}
            >
              {loading ? 'Processing...' : rateLoading ? 'Loading...' : !exchangeRate ? 'Rate Unavailable' : `Top Up ${formatUSD(usdAmount)}`}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AITopUpModal;
