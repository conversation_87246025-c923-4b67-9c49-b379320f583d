import React from 'react';
import { X, AlertTriangle, ExternalLink } from 'lucide-react';
import Button from '../common/Button';

interface DeploymentConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  templateName: string;
  isN8nService: boolean;
}

const DeploymentConfirmationModal: React.FC<DeploymentConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  templateName,
  isN8nService
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose}></div>
        
        <div className="relative w-full max-w-md rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-50">
                <AlertTriangle className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Deployment Agreement</h3>
                <p className="text-sm text-gray-500">Please read and agree to continue</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="px-6 py-4">
            <div className="space-y-4">
              {/* General Terms */}
              <div className="rounded-lg bg-yellow-50 border border-yellow-200 p-4">
                <h4 className="text-sm font-semibold text-yellow-800 mb-2">Terms of Service</h4>
                <p className="text-sm text-yellow-700">
                  By deploying this service, you agree that you will not use this service for any illegal activities. 
                  If illegal activities are detected, your service will be terminated automatically without prior notice.
                </p>
              </div>

              {/* n8n Specific Warning */}
              {/* {isN8nService && (
                <div className="rounded-lg bg-blue-50 border border-blue-200 p-4">
                  <h4 className="text-sm font-semibold text-blue-800 mb-2">n8n OAuth Google Limitation</h4>
                  <div className="text-sm text-blue-700 space-y-2">
                    <p>
                      Please note that you cannot use OAuth Google authentication with this n8n deployment.
                    </p>
                    <p>
                      If you need OAuth Google functionality, please contact{' '}
                      <a 
                        href="mailto:<EMAIL>" 
                        className="font-medium underline hover:no-underline"
                      >
                        <EMAIL>
                      </a>{' '}
                      to set up your own custom domain.
                    </p>
                    <p>
                      Alternatively, you can use Google Service Account authentication. Learn more:
                    </p>
                    <a 
                      href="https://docs.n8n.io/integrations/builtin/credentials/google/service-account/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
                    >
                      n8n Service Account Documentation
                      <ExternalLink size={14} className="ml-1" />
                    </a>
                  </div>
                </div>
              )} */}
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 border-t border-gray-200 px-6 py-4">
            <Button
              variant="secondary"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={onConfirm}
            >
              I Agree, Deploy {templateName}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeploymentConfirmationModal;
