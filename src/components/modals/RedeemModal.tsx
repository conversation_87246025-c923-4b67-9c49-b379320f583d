import React, { useState, useCallback } from 'react';
import { X, AlertCircle, CheckCircle, Ticket } from 'lucide-react';
import Button from '../common/Button';
import Turnstile from '../common/Turnstile';
import { supabase } from '../../lib/supabase';

interface RedeemModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RedeemModal: React.FC<RedeemModalProps> = ({ isOpen, onClose }) => {
  const [code, setCode] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Turnstile state
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  const [turnstileKey, setTurnstileKey] = useState(0);

  // Stable callback functions for Turnstile to prevent re-rendering loops
  const handleTurnstileVerify = useCallback((token: string) => {
    setTurnstileToken(token);
  }, []);

  const handleTurnstileError = useCallback(() => {
    setTurnstileToken(null);
  }, []);

  const handleTurnstileExpire = useCallback(() => {
    setTurnstileToken(null);
  }, []);

  if (!isOpen) return null;

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Convert to uppercase automatically
    setCode(e.target.value.toUpperCase());
    // Clear any previous error when user starts typing
    if (error) setError(null);
    if (success) setSuccess(false);
  };

  const handleSubmit = async () => {
    if (!code.trim()) {
      setError('Please enter a voucher code');
      return;
    }

    if (!turnstileToken) {
      setError('Please complete the security verification');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Get the user's session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        setError('User not authenticated');
        return;
      }

      // Call the redeem API
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/redeem', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          code: code.trim(),
          turnstile_token: turnstileToken
        })
      });

      if (response.status === 200) {
        // Success - show success message and reload page after a short delay
        setSuccess(true);
        setError(null);
        // Reset Turnstile token
        setTurnstileToken(null);
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        // Handle error responses
        const errorData = await response.json().catch(() => ({}));

        if (response.status === 400) {
          if (errorData.code === 'CAPTCHA_ERROR') {
            setError('Security verification failed. Please try again.');
            // Reset Turnstile token to force re-verification
            setTurnstileToken(null);
            setTurnstileKey(prev => prev + 1);
          } else {
            setError(errorData.message || 'An error occurred while redeeming the voucher');
          }
        } else if (response.status === 404) {
          setError('Coupon not found or already redeemed');
        } else {
          setError(errorData.message || 'An error occurred while redeeming the voucher');
        }
      }
    } catch (err) {
      console.error('Error redeeming voucher:', err);
      setError('An unexpected error occurred');
      // Reset Turnstile token on error
      setTurnstileToken(null);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setCode('');
      setError(null);
      setSuccess(false);
      setTurnstileToken(null);
      setTurnstileKey(prev => prev + 1);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={handleClose} />

        <div className="relative w-full max-w-md rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Redeem Voucher</h3>
            <button
              onClick={handleClose}
              disabled={loading}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 disabled:opacity-50"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="space-y-4">
              {/* Voucher Code Input */}
              <div>
                <label htmlFor="voucher-code" className="block text-sm font-medium text-gray-700">
                  Voucher Code
                </label>
                <div className="mt-1 relative">
                  <input
                    type="text"
                    id="voucher-code"
                    value={code}
                    onChange={handleCodeChange}
                    className="block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm uppercase"
                    placeholder="Enter voucher code"
                    disabled={loading || success}
                    autoFocus
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <Ticket className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Enter your voucher code to redeem credits
                </p>
              </div>

              {/* Security Verification */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-800 mb-3">Security Verification</div>
                <div className="flex justify-center">
                  <Turnstile
                    key={turnstileKey}
                    onVerify={handleTurnstileVerify}
                    onError={handleTurnstileError}
                    onExpire={handleTurnstileExpire}
                  />
                </div>
                <p className="text-xs text-gray-600 mt-2 text-center">
                  Please complete the security verification to proceed with redeeming.
                </p>
              </div>
            </div>
          </div>

          {/* Success message */}
          {success && (
            <div className="px-6 py-2">
              <div className="flex items-center text-green-600 text-sm">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span>Voucher redeemed successfully! Reloading page...</span>
              </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="px-6 py-2">
              <div className="flex items-center text-red-600 text-sm">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="border-t border-gray-200 px-6 py-4">
            <div className="flex space-x-3">
              <Button
                variant="secondary"
                className="flex-1"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                className="flex-1"
                onClick={handleSubmit}
                disabled={!code.trim() || loading || success || !turnstileToken}
                isLoading={loading}
              >
                Redeem
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RedeemModal;
