import React, { useState, useEffect } from 'react';
import { X, Info, AlertCircle } from 'lucide-react';
import Button from '../common/Button';
import { APIKey } from '../../types/ai';

interface EditAPIKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (keyId: string, name: string, creditLimit?: number) => void;
  apiKey: APIKey | null;
}

const EditAPIKeyModal: React.FC<EditAPIKeyModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  apiKey,
}) => {
  const [name, setName] = useState('');
  const [creditLimit, setCreditLimit] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (apiKey) {
      setName(apiKey.key_alias);
      setCreditLimit(apiKey.max_budget ? apiKey.max_budget.toString() : '');
    }
  }, [apiKey]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || !apiKey) return;

    setIsSubmitting(true);
    setError('');
    try {
      const limit = creditLimit.trim() ? parseFloat(creditLimit) : undefined;

      // Validate that credit limit is not negative
      if (limit !== undefined && limit < 0) {
        setError('Credit limit cannot be negative');
        setIsSubmitting(false);
        return;
      }

      await onSubmit(apiKey.id, name.trim(), limit);
    } catch (error) {
      console.error('Error updating API key:', error);
      setError(error instanceof Error ? error.message : 'Failed to update API key');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setName('');
    setCreditLimit('');
    setError('');
    onClose();
  };

  if (!isOpen || !apiKey) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Edit API Key</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Name
              </label>
              <Info className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder='e.g. "Chatbot Key"'
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
              autoFocus
            />
          </div>

          <div>
            <div className="flex items-center space-x-2 mb-2">
              <label htmlFor="creditLimit" className="block text-sm font-medium text-gray-700">
                Credit limit (optional)
              </label>
              <Info className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="number"
              id="creditLimit"
              value={creditLimit}
              onChange={(e) => setCreditLimit(e.target.value)}
              placeholder="Leave blank for unlimited"
              min="0"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Set a spending limit in USD for this API key
            </p>
          </div>

          {/* Error message */}
          {error && (
            <div className="flex items-center text-red-600 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              <span>{error}</span>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              onClick={handleClose}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!name.trim() || isSubmitting}
              className="bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditAPIKeyModal;
