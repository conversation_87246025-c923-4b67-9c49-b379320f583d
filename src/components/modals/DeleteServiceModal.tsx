import React, { useState } from 'react';
import { AlertTriangle, Clock, CreditCard } from 'lucide-react';
import Button from '../common/Button';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase';

interface DeleteServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  serviceName: string;
  serviceId: string;
  serviceExpiresAt: string;
  servicePrice: number;
  servicePlan: string;
}

const DeleteServiceModal: React.FC<DeleteServiceModalProps> = ({
  isOpen,
  onClose,
  serviceName,
  serviceId,
  serviceExpiresAt,
  servicePrice,
  servicePlan
}) => {
  const [confirmText, setConfirmText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const expectedConfirmation = `delete ${serviceName}`;

  // Format price in Indonesian Rupiah with currency symbol
  const formatPrice = (price: number) => {
    return 'Rp ' + new Intl.NumberFormat('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Calculate remaining days
  const calculateRemainingDays = () => {
    const now = new Date();
    const expiry = new Date(serviceExpiresAt);
    const diffTime = Math.max(expiry.getTime() - now.getTime(), 0);
    const remainingDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(remainingDays, 0);
  };

  const remainingDays = calculateRemainingDays();

  const handleDelete = async () => {
    if (confirmText !== expectedConfirmation) {
      setError('Please type the confirmation text exactly as shown');
      return;
    }

    try {
      setIsDeleting(true);
      setError(null);

      // Call Supabase RPC function to delete the service
      // This ensures the trigger for quota management is executed
      const { error: deleteError } = await supabase
        .from('services')
        .delete()
        .eq('id', serviceId);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      // Redirect to services list on success
      navigate('/dashboard/services');
    } catch (err: any) {
      console.error('Error deleting service:', err);
      setError(err.message || 'Failed to delete service');
      setIsDeleting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden">
        <div className="bg-red-50 p-4 border-b border-red-100">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-red-800">Delete Service</h3>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="mb-4">
            <p className="text-gray-700 mb-2">
              Are you sure you want to delete this service? This action cannot be undone.
            </p>
            <p className="text-gray-700 mb-4">
              All data associated with <span className="font-semibold">{serviceName}</span> will be permanently deleted.
            </p>

            {/* Warning about remaining validity and no refund */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                <div className="space-y-2">
                  <p className="text-sm font-medium text-red-800">Important Notice:</p>
                  <div className="space-y-1 text-sm text-red-700">
                    {remainingDays > 0 && (
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span>Remaining validity period of <strong>{remainingDays} days</strong> will be lost</span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <CreditCard className="h-4 w-4 mr-2 flex-shrink-0" />
                      <span>Credits worth <strong>{formatPrice(servicePrice)}</strong> will not be refunded</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 p-3 rounded-md border border-gray-200 mb-4">
              <p className="text-sm text-gray-600 mb-2">
                To confirm, type <span className="font-mono font-bold">delete {serviceName}</span> below:
              </p>
              <input
                type="text"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder={`delete ${serviceName}`}
                disabled={isDeleting}
              />
            </div>

            {error && (
              <div className="text-red-600 text-sm mb-4">
                {error}
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="secondary"
              onClick={onClose}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleDelete}
              isLoading={isDeleting}
              disabled={confirmText !== expectedConfirmation || isDeleting}
            >
              Delete Service
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteServiceModal;
