import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ExternalLink, Github, ArrowRight } from 'lucide-react';
import Button from '../common/Button';
import { StaticTemplate } from '../../hooks/useStaticTemplates';
import { useAuth } from '../../context/AuthContext';

// Add CSS for description container with 2-line limit and ellipsis
const descriptionStyles = `
  .description-container {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
    min-height: 2.6em;
  }
`;

interface StaticTemplateCardProps {
  template: StaticTemplate;
}

const StaticTemplateCard: React.FC<StaticTemplateCardProps> = ({ template }) => {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  // Format price to IDR
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Generate deploy URL with redirect if not authenticated
  const getDeployUrl = () => {
    const deployUrl = `/dashboard/services/create/${template.id}`;
    if (isAuthenticated) {
      return deployUrl;
    } else {
      // If not authenticated, redirect to login with redirect parameter
      return `/login?redirect=${encodeURIComponent(deployUrl)}`;
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      {/* Apply the CSS styles */}
      <style dangerouslySetInnerHTML={{ __html: descriptionStyles }} />

      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center justify-center w-12 h-12 bg-gray-50 rounded-lg overflow-hidden">
            {template.logoUrl ? (
              <img
                src={template.logoUrl}
                alt={`${template.name} logo`}
                className="w-8 h-8 object-contain"
                onError={(e) => {
                  // Fallback to icon if logo fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
            ) : null}
            <div className={`${template.logoUrl ? 'hidden' : ''}`}>
              {template.icon}
            </div>
          </div>
          <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {template.category}
          </div>
        </div>

        <h3 className="text-lg font-medium text-gray-900 mb-2">{template.name}</h3>
        <p className="text-sm text-gray-500 mb-4 description-container">{template.description}</p>

        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-4">
          {template.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-block bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded"
            >
              {tag}
            </span>
          ))}
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="text-lg font-bold text-gray-900">
            {formatPrice(template.price)}
          </div>
          <div className="text-xs text-gray-500">/month</div>
        </div>

        {/* Action buttons */}
        <div className="flex justify-between items-center mb-4">
          <Link to={getDeployUrl()}>
            <Button variant="primary" size="sm" className="flex items-center">
              Deploy Now
              <ArrowRight size={14} className="ml-1" />
            </Button>
          </Link>
          <div className="flex space-x-2">
            {template.github && (
              <a
                href={template.github}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                title="View on GitHub"
              >
                <Github size={16} />
              </a>
            )}
            {template.website && (
              <a
                href={template.website}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                title="Visit website"
              >
                <ExternalLink size={16} />
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StaticTemplateCard;
