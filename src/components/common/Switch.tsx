import React from 'react';

interface SwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  size?: 'sm' | 'md';
  className?: string;
}

const Switch: React.FC<SwitchProps> = ({
  checked,
  onChange,
  disabled = false,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: {
      container: 'w-9 h-5',
      toggle: 'w-4 h-4',
      translateChecked: 'translate-x-4',
      translateUnchecked: 'translate-x-0.5'
    },
    md: {
      container: 'w-11 h-6',
      toggle: 'w-5 h-5',
      translateChecked: 'translate-x-5',
      translateUnchecked: 'translate-x-0.5'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <button
      type="button"
      className={`
        relative inline-flex items-center ${currentSize.container} flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent
        transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
        ${checked
          ? 'bg-blue-600 hover:bg-blue-700'
          : 'bg-gray-200 hover:bg-gray-300'
        }
        ${disabled
          ? 'opacity-50 cursor-not-allowed' + (checked ? ' hover:bg-blue-600' : ' hover:bg-gray-200')
          : ''
        }
        ${className}
      `}
      role="switch"
      aria-checked={checked}
      onClick={() => !disabled && onChange(!checked)}
      disabled={disabled}
    >
      <span className="sr-only">Toggle switch</span>
      <span
        aria-hidden="true"
        className={`
          ${currentSize.toggle} inline-block rounded-full bg-white shadow-sm transform ring-0
          transition-transform duration-200 ease-in-out
          ${checked ? currentSize.translateChecked : currentSize.translateUnchecked}
        `}
      />
    </button>
  );
};

export default Switch;
