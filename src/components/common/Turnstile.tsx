import React, { useEffect, useRef, useState } from 'react';

interface TurnstileProps {
  onVerify: (token: string) => void;
  onError?: () => void;
  onExpire?: () => void;
  className?: string;
  reset?: boolean;
}

declare global {
  interface Window {
    turnstile: {
      render: (element: HTMLElement | string, options: {
        sitekey: string;
        callback: (token: string) => void;
        'error-callback'?: () => void;
        'expired-callback'?: () => void;
        theme?: 'light' | 'dark';
        size?: 'normal' | 'compact';
      }) => string;
      reset: (widgetId?: string) => void;
      remove: (widgetId?: string) => void;
    };
  }
}

const Turnstile: React.FC<TurnstileProps> = ({
  onVerify,
  onError,
  onExpire,
  className = '',
  reset = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const widgetIdRef = useRef<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isRendered, setIsRendered] = useState(false);

  // Reset widget when reset prop changes
  useEffect(() => {
    if (reset && widgetIdRef.current && window.turnstile) {
      try {
        window.turnstile.reset(widgetIdRef.current);
      } catch (error) {
        console.error('Error resetting Turnstile:', error);
      }
    }
  }, [reset]);

  useEffect(() => {
    const siteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY;

    if (!siteKey) {
      console.error('Turnstile site key not found in environment variables');
      onError?.();
      return;
    }

    const renderTurnstile = () => {
      if (containerRef.current && window.turnstile) {
        try {
          // Clear container first
          containerRef.current.innerHTML = '';

          // Remove existing widget if any
          if (widgetIdRef.current) {
            try {
              window.turnstile.remove(widgetIdRef.current);
            } catch (e) {
              // Ignore errors when removing non-existent widgets
            }
            widgetIdRef.current = null;
          }

          // Render new widget
          widgetIdRef.current = window.turnstile.render(containerRef.current, {
            sitekey: siteKey,
            callback: (token: string) => {
              console.log('Turnstile verification successful');
              onVerify(token);
            },
            'error-callback': () => {
              console.error('Turnstile verification failed');
              onError?.();
            },
            'expired-callback': () => {
              console.log('Turnstile token expired');
              onExpire?.();
            },
            theme: 'light',
            size: 'normal'
          });

          setIsRendered(true);
          console.log('Turnstile widget rendered with ID:', widgetIdRef.current);
        } catch (error) {
          console.error('Error rendering Turnstile:', error);
          onError?.();
        }
      }
    };

    // Check if Turnstile is already loaded
    if (window.turnstile) {
      renderTurnstile();
    } else {
      // Wait for Turnstile to load
      const checkTurnstile = setInterval(() => {
        if (window.turnstile) {
          clearInterval(checkTurnstile);
          renderTurnstile();
        }
      }, 100);

      // Cleanup interval after 10 seconds
      const timeout = setTimeout(() => {
        clearInterval(checkTurnstile);
        if (!window.turnstile) {
          console.error('Turnstile failed to load within 10 seconds');
          onError?.();
        }
      }, 10000);

      return () => {
        clearInterval(checkTurnstile);
        clearTimeout(timeout);
      };
    }

    // Cleanup function
    return () => {
      if (widgetIdRef.current && window.turnstile) {
        try {
          window.turnstile.remove(widgetIdRef.current);
          widgetIdRef.current = null;
          setIsRendered(false);
        } catch (error) {
          console.error('Error removing Turnstile widget:', error);
        }
      }
    };
  }, [onVerify, onError, onExpire]);

  return (
    <div className={`turnstile-container ${className}`}>
      <div ref={containerRef} />
    </div>
  );
};

export default Turnstile;
