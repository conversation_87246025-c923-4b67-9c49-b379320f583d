import React, { useEffect } from 'react';
import { X, AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

interface ToastProps {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  onClose,
}) => {
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        onClose(id);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [id, duration, onClose]);

  const getToastStyles = () => {
    switch (type) {
      case 'success':
        return {
          container: 'bg-green-50 border-green-200',
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          title: 'text-green-800',
          message: 'text-green-700',
        };
      case 'error':
        return {
          container: 'bg-red-50 border-red-200',
          icon: <AlertCircle className="h-5 w-5 text-red-500" />,
          title: 'text-red-800',
          message: 'text-red-700',
        };
      case 'warning':
        return {
          container: 'bg-yellow-50 border-yellow-200',
          icon: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
          title: 'text-yellow-800',
          message: 'text-yellow-700',
        };
      case 'info':
        return {
          container: 'bg-blue-50 border-blue-200',
          icon: <Info className="h-5 w-5 text-blue-500" />,
          title: 'text-blue-800',
          message: 'text-blue-700',
        };
      default:
        return {
          container: 'bg-gray-50 border-gray-200',
          icon: <Info className="h-5 w-5 text-gray-500" />,
          title: 'text-gray-800',
          message: 'text-gray-700',
        };
    }
  };

  const styles = getToastStyles();

  return (
    <div className={`w-full ${styles.container} border rounded-lg shadow-lg p-4 transition-all duration-300 ease-in-out transform translate-x-0 opacity-100`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {styles.icon}
        </div>
        <div className="ml-3 flex-1 min-w-0">
          <p className={`text-sm font-medium ${styles.title} break-words`}>
            {title}
          </p>
          {message && (
            <p className={`mt-1 text-sm ${styles.message} break-words`}>
              {message}
            </p>
          )}
        </div>
        <div className="ml-4 flex-shrink-0 flex">
          <button
            className="inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            onClick={() => onClose(id)}
          >
            <span className="sr-only">Close</span>
            <X className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Toast;
