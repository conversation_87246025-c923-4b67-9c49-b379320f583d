import React from 'react';
import { Server } from 'lucide-react';
import Button from './Button';
import { Link } from 'react-router-dom';

interface CreateVPSButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
}

const CreateVPSButton: React.FC<CreateVPSButtonProps> = ({
  variant = 'primary',
  size = 'md'
}) => {
  return (
    <Button
      as={Link}
      to="/dashboard/vps/create"
      variant={variant}
      size={size}
    >
      <Server size={size === 'sm' ? 16 : 18} className="mr-2" />
      Create VPS
    </Button>
  );
};

export default CreateVPSButton;
