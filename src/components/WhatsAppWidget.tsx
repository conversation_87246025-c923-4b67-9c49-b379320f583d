import React, { useState } from 'react';
import { MessageCircle, X, Send } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

interface WhatsAppWidgetProps {
  phoneNumber: string;
  companyName?: string;
}

const WhatsAppWidget: React.FC<WhatsAppWidgetProps> = ({
  phoneNumber,
  companyName = 'Sumopod'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  // Check if current route is a dashboard route
  const isDashboardRoute = location.pathname.startsWith('/dashboard');

  // Only show widget if user is authenticated and on dashboard pages
  if (!isAuthenticated || !isDashboardRoute) {
    return null;
  }

  const handleSendMessage = () => {
    if (message.trim()) {
      const encodedMessage = encodeURIComponent(message);
      const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
      window.open(whatsappUrl, '_blank');
      setMessage('');
      setIsOpen(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* WhatsApp Button */}
      <div className="fixed right-6 z-50 bottom-32 md:bottom-6">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-2xl transition-all duration-300 hover:scale-105 border-4 border-white"
          aria-label="Open WhatsApp Chat"
        >
          {isOpen ? (
            <X className="h-7 w-7" />
          ) : (
            <MessageCircle className="h-7 w-7" />
          )}
        </button>

        {/* Notification dot */}
        {!isOpen && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white"></div>
        )}
      </div>

      {/* Chat Popup */}
      {isOpen && (
        <div className="fixed right-4 left-4 md:right-6 md:left-auto z-50 md:w-80 bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden bottom-48 md:bottom-24">
          {/* Header */}
          <div className="bg-green-500 text-white p-4 flex items-center justify-between rounded-t-2xl">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <MessageCircle className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">{companyName}</h3>
                <p className="text-sm text-green-100 opacity-90">Typically replies within a minute</p>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-all"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Chat Content */}
          <div className="p-6 bg-gray-50 min-h-[280px] flex flex-col">
            {/* Welcome Message */}
            <div className="bg-white rounded-2xl p-4 mb-6 shadow-sm border border-gray-100">
              <p className="text-gray-900 font-semibold text-lg mb-1">Hi, Welcome to {companyName}</p>
              <p className="text-gray-600 text-base">How can we help you?</p>
              <span className="text-xs text-gray-400 mt-3 block">
                {new Date().toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true
                }).toUpperCase()}
              </span>
            </div>

            {/* Message Input */}
            <div className="mt-auto space-y-4">
              <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type Message"
                  className="w-full p-4 border-0 rounded-2xl resize-none focus:outline-none text-gray-700 placeholder-gray-400 bg-transparent"
                  rows={3}
                />
              </div>

              {/* Send Button */}
              <button
                onClick={handleSendMessage}
                disabled={!message.trim()}
                className="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-200 shadow-sm"
              >
                Send A Message
              </button>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-white px-4 py-3 text-center border-t border-gray-100">
            <span className="text-xs text-gray-400">Powered by WhatsApp</span>
          </div>
        </div>
      )}
    </>
  );
};

export default WhatsAppWidget;
