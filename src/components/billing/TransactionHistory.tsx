import React from 'react';
import { <PERSON>U<PERSON>, <PERSON>Down, Refresh<PERSON><PERSON>, Gift } from 'lucide-react';
import { Transaction } from '../../services/billingService';
import { formatNumber } from '../../utils/formatters';
import Pagination from './Pagination';

interface TransactionHistoryProps {
  transactions: Transaction[];
  loading: boolean;
  pagination: {
    page: number;
    perPage: number;
    totalPages: number;
    totalItems: number;
    setPage: (page: number) => void;
  };
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({
  transactions,
  loading,
  pagination
}) => {
  if (loading) {
    return (
      <div className="text-center py-12">
        <p>Loading transactions...</p>
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No transactions found</p>
      </div>
    );
  }

  return (
    <div>
      <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Date
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Description
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Type
            </th>
            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Amount
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {transactions.map((transaction) => (
            <tr key={transaction.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {new Date(transaction.createdAt).toLocaleDateString()}
              </td>
              <td className="px-6 py-4 text-sm text-gray-900">
                {transaction.description}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm">
                <span className="inline-flex items-center">
                  {transaction.transactionType === 'purchase' && (
                    <ArrowUp className="h-4 w-4 mr-1 text-green-500" />
                  )}
                  {transaction.transactionType === 'usage' && (
                    <ArrowDown className="h-4 w-4 mr-1 text-red-500" />
                  )}
                  {transaction.transactionType === 'refund' && (
                    <RefreshCw className="h-4 w-4 mr-1 text-blue-500" />
                  )}
                  {transaction.transactionType === 'bonus' && (
                    <Gift className="h-4 w-4 mr-1 text-purple-500" />
                  )}
                  <span className={`capitalize ${
                    transaction.transactionType === 'purchase' || transaction.transactionType === 'bonus'
                      ? 'text-green-600'
                      : transaction.transactionType === 'refund'
                        ? 'text-blue-600'
                        : 'text-red-600'
                  }`}>
                    {transaction.transactionType}
                  </span>
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                <span className={`font-medium ${
                  transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {transaction.amount > 0 ? '+' : ''}Rp {formatNumber(transaction.amount, 0)} credits
                </span>
              </td>
            </tr>
          ))}
        </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            onPageChange={pagination.setPage}
          />
        </div>
      )}
    </div>
  );
};

export default TransactionHistory;
