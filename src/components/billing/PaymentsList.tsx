import React, { useState } from 'react';
import { CheckCir<PERSON>, Clock, XCircle, Download } from 'lucide-react';
import { Payment } from '../../services/billingService';
import { formatIDR, formatDateWithTimezone } from '../../utils/formatters';
import { downloadInvoiceAsPDF, InvoiceData } from '../../utils/invoiceGenerator';
import Button from '../common/Button';
import Pagination from './Pagination';
import { useAuth } from '../../context/AuthContext';

interface PaymentsListProps {
  payments: Payment[];
  loading: boolean;
  pagination?: {
    page: number;
    perPage: number;
    totalPages: number;
    totalItems: number;
    setPage: (page: number) => void;
  };
}

const PaymentsList: React.FC<PaymentsListProps> = ({
  payments,
  loading,
  pagination
}) => {
  const { user } = useAuth();

  const handlePayment = (payment: Payment) => {
    if (payment.paymentUrl) {
      // Open payment URL in new tab
      window.open(payment.paymentUrl, '_blank');
    }
  };



  const handleDownloadInvoice = async (payment: Payment) => {
    try {
      const invoiceData: InvoiceData = {
        payment,
        userEmail: user?.email || '',
        userName: user?.user_metadata?.full_name || user?.user_metadata?.name,
        issueDate: formatDateWithTimezone(payment.createdAt, { dateFormat: 'long' })
      };

      await downloadInvoiceAsPDF(invoiceData);
    } catch (error) {
      console.error('Error downloading invoice:', error);
      alert('Failed to download invoice. Please try again.');
    }
  };

  const getStatusBadge = (status: Payment['status']) => {
    switch (status) {
      case 'completed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Completed
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            Failed
          </span>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <p>Loading payments...</p>
      </div>
    );
  }

  return (
    <div>
      {payments.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500">No payments found</p>
        </div>
      ) : (
        <div>
          <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Credits
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {payments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(payment.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {formatIDR(payment.amount, 0)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {payment.credits.toLocaleString()} credits
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(payment.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      {payment.status === 'pending' && (
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => handlePayment(payment)}
                        >
                          Pay
                        </Button>
                      )}
                      {payment.status === 'completed' && (
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => handleDownloadInvoice(payment)}
                        >
                          <Download className="w-4 h-4 mr-1" />
                          Invoice
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.totalPages}
                onPageChange={pagination.setPage}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PaymentsList;
