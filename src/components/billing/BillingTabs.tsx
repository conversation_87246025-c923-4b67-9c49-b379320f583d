import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { CreditCard, Receipt } from 'lucide-react';

const BillingTabs: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const tabs = [
    {
      name: 'Transactions',
      key: 'transactions',
      path: '/dashboard/billing',
      icon: <CreditCard size={16} />
    },
    {
      name: 'Payments',
      key: 'payments',
      path: '/dashboard/billing/payments',
      icon: <Receipt size={16} />
    }
  ];

  const handleTabClick = (path: string) => {
    navigate(path);
  };

  const isActiveTab = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="border-b border-gray-200 bg-gray-50">
      <nav className="flex space-x-1 p-1">
        {tabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => handleTabClick(tab.path)}
            className={`
              flex items-center px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200
              ${isActiveTab(tab.path)
                ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
              }
            `}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.name}
          </button>
        ))}
      </nav>
    </div>
  );
};

export default BillingTabs;
