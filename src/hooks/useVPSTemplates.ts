import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

export interface VPSTemplate {
  id: string;
  name: string;
  type: string;
  description: string;
  base_price: number;
  available_plans: {
    plans: ('monthly' | 'quarterly' | 'biannual' | 'yearly')[];
  };
  specs: {
    cpu: number;
    memory: number;
    storage: number;
  };
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export const useVPSTemplates = () => {
  const [templates, setTemplates] = useState<VPSTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data for demonstration
  useEffect(() => {
    const mockTemplates: VPSTemplate[] = [
      {
        id: '1',
        name: 'Basic VPS',
        type: 'Standard',
        description: 'Ideal for small websites, development environments, and basic applications.',
        base_price: 50000,
        available_plans: {
          plans: ['monthly', 'quarterly', 'yearly']
        },
        specs: {
          cpu: 1,
          memory: 2,
          storage: 20
        },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        is_active: true
      },
      {
        id: '2',
        name: 'Standard VPS',
        type: 'Standard',
        description: 'Perfect for production websites, small databases, and medium-sized applications.',
        base_price: 100000,
        available_plans: {
          plans: ['monthly', 'quarterly', 'biannual', 'yearly']
        },
        specs: {
          cpu: 2,
          memory: 4,
          storage: 50
        },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        is_active: true
      },
      {
        id: '3',
        name: 'Performance VPS',
        type: 'Performance',
        description: 'High-performance VPS for databases, application servers, and resource-intensive workloads.',
        base_price: 200000,
        available_plans: {
          plans: ['monthly', 'quarterly', 'yearly']
        },
        specs: {
          cpu: 4,
          memory: 8,
          storage: 100
        },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        is_active: true
      },
      {
        id: '4',
        name: 'Enterprise VPS',
        type: 'Enterprise',
        description: 'Enterprise-grade VPS for high-traffic websites, large databases, and enterprise applications.',
        base_price: 400000,
        available_plans: {
          plans: ['monthly', 'quarterly', 'yearly']
        },
        specs: {
          cpu: 8,
          memory: 16,
          storage: 200
        },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        is_active: true
      },
      {
        id: '5',
        name: 'GPU VPS',
        type: 'Specialized',
        description: 'VPS with GPU acceleration for machine learning, AI, and graphics-intensive applications.',
        base_price: 600000,
        available_plans: {
          plans: ['monthly', 'quarterly', 'yearly']
        },
        specs: {
          cpu: 8,
          memory: 32,
          storage: 500
        },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        is_active: true
      }
    ];

    setTemplates(mockTemplates);
    setLoading(false);
  }, []);

  // Filter templates based on search query
  const filteredTemplates = templates.filter(template => {
    return (
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  return {
    templates: filteredTemplates,
    loading,
    error,
    searchQuery,
    setSearchQuery
  };
};
