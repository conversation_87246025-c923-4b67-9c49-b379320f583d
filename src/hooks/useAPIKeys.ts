import { useState, useEffect } from 'react';
import { APIKey } from '../types/ai';
import { aiService } from '../services/aiService';
import { useAuth } from '../context/AuthContext';

export const useAPIKeys = () => {
  const { isAuthenticated } = useAuth();

  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [totalApiKeys, setTotalApiKeys] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    const fetchAPIKeys = async () => {
      try {
        setLoading(true);
        const { data: apiKeysData, count } = await aiService.getAPIKeys();

        setApiKeys(apiKeysData);
        setTotalApiKeys(count);
        setError(null);
      } catch (err) {
        console.error('Error fetching API keys:', err);
        setError('Failed to load API keys');
      } finally {
        setLoading(false);
      }
    };

    fetchAPIKeys();
  }, [isAuthenticated]);

  // Function to refresh data
  const refreshData = async () => {
    try {
      setLoading(true);
      const { data: apiKeysData, count } = await aiService.getAPIKeys();
      setApiKeys(apiKeysData);
      setTotalApiKeys(count);
      setError(null);
    } catch (err) {
      console.error('Error refreshing API keys:', err);
      setError('Failed to refresh API keys');
    } finally {
      setLoading(false);
    }
  };

  return {
    apiKeys,
    loading,
    error,
    refreshData,
    totalCount: totalApiKeys,
  };
};
