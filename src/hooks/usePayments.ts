import { useState, useEffect } from 'react';
import { billingService, UserBalance, Payment } from '../services/billingService';
import { useAuth } from '../context/AuthContext';
import { useSearchParams } from 'react-router-dom';

export const usePayments = () => {
  const { isAuthenticated } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const page = parseInt(searchParams.get('page') || '1');
  const perPage = 10; // Number of payments per page

  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [totalPayments, setTotalPayments] = useState(0);
  const [loading, setLoading] = useState({
    balance: true,
    payments: true
  });
  const [error, setError] = useState<string | null>(null);

  // Fetch user balance and payments when authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      setUserBalance(null);
      setPayments([]);
      return;
    }

    const fetchUserData = async () => {
      try {
        // Fetch user balance
        setLoading(prev => ({ ...prev, balance: true }));
        const balance = await billingService.getUserBalance();
        setUserBalance(balance);

        // Fetch payments with pagination
        setLoading(prev => ({ ...prev, payments: true }));
        const paymentOffset = (page - 1) * perPage;
        const { data: paymentsData, count: paymentsCount } = await billingService.getPayments(perPage, paymentOffset);
        setPayments(paymentsData);
        setTotalPayments(paymentsCount);

        setError(null);
      } catch (err) {
        console.error('Error fetching user payments data:', err);
        setError('Failed to load payments data');
      } finally {
        setLoading(prev => ({
          ...prev,
          balance: false,
          payments: false
        }));
      }
    };

    fetchUserData();
  }, [isAuthenticated, page, perPage]);

  // Function to change page
  const setPage = (newPage: number) => {
    setSearchParams({ page: newPage.toString() });
  };

  // Function to refresh all data
  const refreshData = async () => {
    try {
      setLoading(prev => ({ ...prev, balance: true, payments: true }));

      const balance = await billingService.getUserBalance();
      setUserBalance(balance);

      const paymentOffset = (page - 1) * perPage;
      const { data: paymentsData, count: paymentsCount } = await billingService.getPayments(perPage, paymentOffset);
      setPayments(paymentsData);
      setTotalPayments(paymentsCount);

      setError(null);
    } catch (err) {
      console.error('Error refreshing payments data:', err);
      setError('Failed to refresh payments data');
    } finally {
      setLoading(prev => ({ ...prev, balance: false, payments: false }));
    }
  };

  // Calculate total pages for payments
  const totalPaymentPages = Math.max(1, Math.ceil(totalPayments / perPage));

  return {
    userBalance,
    payments,
    loading,
    error,
    refreshData,
    // Pagination properties for payments
    paymentPagination: {
      page,
      perPage,
      totalPages: totalPaymentPages,
      totalItems: totalPayments,
      setPage
    }
  };
};
