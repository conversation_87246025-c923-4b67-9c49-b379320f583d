import { useState, useMemo } from 'react';
import * as Icons from 'lucide-react';
import { Box } from 'lucide-react';
import templatesData from '../data/templates.json';

export interface StaticTemplate {
  id: string;
  name: string;
  description: string;
  iconName: string;
  logoUrl?: string;
  icon: React.ReactNode;
  tags: string[];
  category: string;
  price: number;
  github: string;
  website: string;
}

export const useStaticTemplates = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const templates = useMemo(() => {
    return templatesData.templates.map(template => {
      const IconComponent = Icons[template.iconName as keyof typeof Icons];

      const icon = typeof IconComponent === 'function'
        ? <IconComponent className="h-8 w-8 text-blue-600" />
        : <Box className="h-8 w-8 text-blue-600" />;

      return {
        ...template,
        icon
      };
    });
  }, []);

  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      const matchesSearch = searchQuery === '' ||
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesCategory = selectedCategory === 'All' || template.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  }, [templates, searchQuery, selectedCategory]);

  const categories = templatesData.categories;

  return {
    templates: filteredTemplates,
    allTemplates: templates,
    categories,
    searchQuery,
    setSearchQuery,
    selectedCategory,
    setSelectedCategory,
    loading: false,
    error: null
  };
};
