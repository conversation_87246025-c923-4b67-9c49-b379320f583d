import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { AIUsage } from '../types/ai';
import { aiService } from '../services/aiService';
import { useAuth } from '../context/AuthContext';

export const useAIUsage = () => {
  const { isAuthenticated } = useAuth();

  const [usage, setUsage] = useState<AIUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    const fetchUsage = async () => {
      try {
        setLoading(true);
        const { data: usageData } = await aiService.getAIUsage();

        setUsage(usageData);
        setError(null);
      } catch (err) {
        console.error('Error fetching AI usage:', err);
        setError('Failed to load usage data');
      } finally {
        setLoading(false);
      }
    };

    fetchUsage();
  }, [isAuthenticated]);

  // Function to refresh data
  const refreshData = async () => {
    try {
      setLoading(true);
      const { data: usageData } = await aiService.getAIUsage();
      setUsage(usageData);
      setError(null);
    } catch (err) {
      console.error('Error refreshing AI usage:', err);
      setError('Failed to refresh usage data');
    } finally {
      setLoading(false);
    }
  };

  return {
    usage,
    loading,
    error,
    refreshData,
  };
};
