import { useState, useEffect } from 'react';
import { AIModel } from '../types/ai';
import { aiService } from '../services/aiService';
import { useAuth } from '../context/AuthContext';

export const useAIModels = () => {
  const { isAuthenticated } = useAuth();

  const [models, setModels] = useState<AIModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    const fetchModels = async () => {
      try {
        setLoading(true);
        const { data: modelsData } = await aiService.getAIModels();

        setModels(modelsData);
        setError(null);
      } catch (err) {
        console.error('Error fetching AI models:', err);
        setError('Failed to load models data');
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, [isAuthenticated]);

  // Function to refresh data
  const refreshData = async () => {
    try {
      setLoading(true);
      const { data: modelsData } = await aiService.getAIModels();
      setModels(modelsData);
      setError(null);
    } catch (err) {
      console.error('Error refreshing AI models:', err);
      setError('Failed to refresh models data');
    } finally {
      setLoading(false);
    }
  };

  return {
    models,
    loading,
    error,
    refreshData,
  };
};
