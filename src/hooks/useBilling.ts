import { useState, useEffect } from 'react';
import { billingService, UserBalance, Transaction, Payment } from '../services/billingService';
import { useAuth } from '../context/AuthContext';
import { useSearchParams } from 'react-router-dom';

export const useBilling = () => {
  const { isAuthenticated } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const currentTab = searchParams.get('tab') || 'transactions';
  const page = parseInt(searchParams.get('page') || '1');
  const perPage = 10; // Number of transactions per page

  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [totalPayments, setTotalPayments] = useState(0);
  const [loading, setLoading] = useState({
    balance: true,
    transactions: true,
    payments: true
  });
  const [error, setError] = useState<string | null>(null);



  // Fetch user balance and transactions when authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      setUserBalance(null);
      setTransactions([]);
      setPayments([]);
      return;
    }

    const fetchUserData = async () => {
      try {
        // Fetch user balance
        setLoading(prev => ({ ...prev, balance: true }));
        const balance = await billingService.getUserBalance();
        setUserBalance(balance);

        // Fetch transactions with pagination (only if on transactions tab)
        if (currentTab === 'transactions') {
          setLoading(prev => ({ ...prev, transactions: true }));
          const offset = (page - 1) * perPage;
          const { data: transactionsData, count } = await billingService.getTransactions(perPage, offset);

          // Set transactions and total count
          setTransactions(transactionsData);
          setTotalTransactions(count);
          setLoading(prev => ({ ...prev, transactions: false }));
        }

        // Fetch payments with pagination (only if on payments tab)
        if (currentTab === 'payments') {
          setLoading(prev => ({ ...prev, payments: true }));
          const paymentOffset = (page - 1) * perPage;
          const { data: paymentsData, count: paymentsCount } = await billingService.getPayments(perPage, paymentOffset);
          setPayments(paymentsData);
          setTotalPayments(paymentsCount);
          setLoading(prev => ({ ...prev, payments: false }));
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching user billing data:', err);
        setError('Failed to load billing data');
      } finally {
        setLoading(prev => ({
          ...prev,
          balance: false
        }));
      }
    };

    fetchUserData();
  }, [isAuthenticated, page, perPage, currentTab]);





  // Function to change page
  const setPage = (newPage: number) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', newPage.toString());
      // Ensure tab parameter is preserved
      if (!newParams.has('tab')) {
        newParams.set('tab', currentTab);
      }
      return newParams;
    });
  };

  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalTransactions / perPage));



  // Function to refresh all data
  const refreshData = async () => {
    try {
      setLoading(prev => ({ ...prev, balance: true }));

      const balance = await billingService.getUserBalance();
      setUserBalance(balance);

      // Only refresh data for the current tab
      if (currentTab === 'transactions') {
        setLoading(prev => ({ ...prev, transactions: true }));
        const offset = (page - 1) * perPage;
        const { data: transactionsData, count } = await billingService.getTransactions(perPage, offset);
        setTransactions(transactionsData);
        setTotalTransactions(count);
        setLoading(prev => ({ ...prev, transactions: false }));
      }

      if (currentTab === 'payments') {
        setLoading(prev => ({ ...prev, payments: true }));
        const paymentOffset = (page - 1) * perPage;
        const { data: paymentsData, count: paymentsCount } = await billingService.getPayments(perPage, paymentOffset);
        setPayments(paymentsData);
        setTotalPayments(paymentsCount);
        setLoading(prev => ({ ...prev, payments: false }));
      }

      setError(null);
    } catch (err) {
      console.error('Error refreshing data:', err);
      setError('Failed to refresh data');
    } finally {
      setLoading(prev => ({ ...prev, balance: false }));
    }
  };

  // Calculate total pages for transactions and payments
  const totalTransactionPages = Math.max(1, Math.ceil(totalTransactions / perPage));
  const totalPaymentPages = Math.max(1, Math.ceil(totalPayments / perPage));

  return {
    userBalance,
    transactions,
    payments,
    loading,
    error,
    refreshData,
    // Pagination properties for transactions
    pagination: {
      page,
      perPage,
      totalPages: totalTransactionPages,
      totalItems: totalTransactions,
      setPage
    },
    // Pagination properties for payments
    paymentPagination: {
      page,
      perPage,
      totalPages: totalPaymentPages,
      totalItems: totalPayments,
      setPage
    }
  };
};
