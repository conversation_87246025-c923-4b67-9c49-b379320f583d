import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { AITransaction } from '../types/ai';
import { aiService } from '../services/aiService';
import { useAuth } from '../context/AuthContext';

export const useAITransactions = () => {
  const { isAuthenticated } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const page = parseInt(searchParams.get('page') || '1');
  const perPage = 10; // Number of transactions per page

  const [transactions, setTransactions] = useState<AITransaction[]>([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    const fetchTransactions = async () => {
      try {
        setLoading(true);
        const offset = (page - 1) * perPage;
        const { data: transactionsData, count } = await aiService.getAITransactions(perPage, offset);

        setTransactions(transactionsData);
        setTotalTransactions(count);
        setError(null);
      } catch (err) {
        console.error('Error fetching AI transactions:', err);
        setError('Failed to load transactions data');
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [isAuthenticated, page, perPage]);

  // Function to change page
  const setPage = (newPage: number) => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('page', newPage.toString());
      return newParams;
    });
  };

  // Function to refresh data
  const refreshData = async () => {
    try {
      setLoading(true);
      const offset = (page - 1) * perPage;
      const { data: transactionsData, count } = await aiService.getAITransactions(perPage, offset);
      setTransactions(transactionsData);
      setTotalTransactions(count);
      setError(null);
    } catch (err) {
      console.error('Error refreshing AI transactions:', err);
      setError('Failed to refresh transactions data');
    } finally {
      setLoading(false);
    }
  };

  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalTransactions / perPage));

  return {
    transactions,
    loading,
    error,
    refreshData,
    // Pagination properties
    pagination: {
      page,
      perPage,
      totalPages,
      totalItems: totalTransactions,
      setPage
    }
  };
};
