import { useState, useMemo, useEffect } from 'react';
import { supabase } from '../lib/supabase';

export interface Service {
  id: string;
  user_id: string;
  template_id: string;
  name: string;
  status: 'active' | 'suspended' | 'stopped' | 'provisioning';
  plan: 'monthly' | 'quarterly' | 'biannual' | 'yearly';
  price: number;
  base_price: number;
  created_at: string;
  updated_at: string;
  expires_at: string;
  schedule_delete_at: string | null;
  admin_url: string | null;
  api_url: string | null;
  public_url: string | null;
  database_url: string | null;
  admin_username: string | null;
  admin_password: string | null;
  api_key: string | null;
  is_auto_renewal: boolean;
  // Join with service_templates
  service_templates?: {
    name: string;
    type: string;
    description: string;
    slug_id: string;
    cpu_limit?: number;
    ram_limit?: number;
    grade?: number;
  };
  // Formatted expiry date (calculated)
  expiryFormatted?: string;
}

export const useServices = (id?: string) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'suspended' | 'stopped' | 'provisioning'>('all');
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [singleService, setSingleService] = useState<Service | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Format expiry date with remaining days (2-line format)
  const formatExpiryWithDays = (dateString: string) => {
    if (!dateString || dateString.trim() === '') {
      return 'Invalid Date\n(Unknown)';
    }

    try {
      const now = new Date();

      // Handle different date formats from Supabase
      let expiryDate: Date;

      // Check if it's already in ISO format (ends with Z)
      if (dateString.includes('Z')) {
        expiryDate = new Date(dateString);
      }
      // Handle PostgreSQL timestamp format (YYYY-MM-DD HH:MM:SS.ssssss+00)
      else if (dateString.includes('+00')) {
        // Replace space and +00 with Z for proper ISO format
        const isoString = dateString.replace(/\s?\+00$/, 'Z').replace(' ', 'T');
        expiryDate = new Date(isoString);
      }
      // Handle PostgreSQL timestamp format without timezone (YYYY-MM-DD HH:MM:SS.ssssss)
      else if (dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)) {
        // Convert to ISO format and assume UTC
        const isoString = dateString.replace(' ', 'T') + 'Z';
        expiryDate = new Date(isoString);
      }
      // Handle other formats by trying to parse directly
      else {
        // Try adding Z if it looks like UTC timestamp
        expiryDate = new Date(dateString + (dateString.includes('T') ? 'Z' : ''));
      }

      // Check if date is valid
      if (isNaN(expiryDate.getTime())) {
        return 'Invalid Date\n(Unknown)';
      }

      // Format the date as DD/MM/YYYY
      const formattedDate = expiryDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });

      // If expired
      if (expiryDate < now) {
        return `${formattedDate}\n(Expired)`;
      }

      const diffTime = Math.abs(expiryDate.getTime() - now.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return `${formattedDate}\n(${diffDays} days left)`;
    } catch (error) {
      console.error('Error formatting expiry date:', error);
      return 'Invalid Date\n(Unknown)';
    }
  };

  // Fetch all services
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const { data, error: apiError } = await supabase
          .from('services')
          .select(`
            *,
            service_templates:template_id (
              name,
              type,
              description,
              slug_id,
              cpu_limit,
              ram_limit,
              grade
            )
          `)
          .order('created_at', { ascending: false });

        if (apiError) {
          throw apiError;
        }

        setServices(data || []);
        setError(null);
      } catch (err: any) {
        console.error('Error fetching services:', err);
        setError(err.message || 'Failed to load services');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, [refreshTrigger]);

  // Fetch single service if ID is provided
  useEffect(() => {
    if (!id) {
      setSingleService(null);
      return;
    }

    const fetchService = async () => {
      try {
        setLoading(true);
        const { data, error: apiError } = await supabase
          .from('services')
          .select(`
            *,
            service_templates:template_id (
              name,
              type,
              description,
              slug_id,
              cpu_limit,
              ram_limit,
              grade
            )
          `)
          .eq('id', id)
          .single();

        if (apiError) {
          throw apiError;
        }

        setSingleService(data);
        setError(null);
      } catch (err: any) {
        console.error(`Error fetching service with id ${id}:`, err);
        setError(err.message || 'Failed to load service details');
      } finally {
        setLoading(false);
      }
    };

    fetchService();
  }, [id, refreshTrigger]);

  // Add formatted expiry date to services
  const servicesWithFormattedExpiry = useMemo(() => {
    return services.map(service => ({
      ...service,
      expiryFormatted: formatExpiryWithDays(service.expires_at)
    }));
  }, [services]);

  const filteredServices = useMemo(() => {
    return servicesWithFormattedExpiry.filter(service => {
      const matchesStatus = statusFilter === 'all' || service.status === statusFilter;
      const matchesSearch = searchQuery === '' ||
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (service.service_templates?.type || '').toLowerCase().includes(searchQuery.toLowerCase());

      return matchesStatus && matchesSearch;
    });
  }, [servicesWithFormattedExpiry, statusFilter, searchQuery]);

  // Function to refresh services data
  const refreshServices = () => {
    setLoading(true);
    setRefreshTrigger(prev => prev + 1);
  };

  // Function to update service auto renewal status
  const updateAutoRenewal = async (serviceId: string, isAutoRenewal: boolean) => {
    try {
      // Get the current session for authentication
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('User must be authenticated');
      }

      // Call the API endpoint
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/service/auto-renewal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          service_id: serviceId,
          is_auto_renewal: isAutoRenewal
        }),
      });

      // Check if response is successful (200)
      if (!response.ok) {
        // Try to get error message from response
        let errorMessage = 'Failed to update auto renewal setting';
        try {
          const errorData = await response.text();
          if (errorData) {
            errorMessage = errorData;
          }
        } catch {
          // If we can't parse the error, use default message
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      // Refresh services data to reflect the change
      refreshServices();
      return true;
    } catch (err: any) {
      console.error('Error updating auto renewal:', err);
      throw new Error(err.message || 'Failed to update auto renewal setting');
    }
  };

  return {
    services: filteredServices,
    service: singleService ? {
      ...singleService,
      expiryFormatted: formatExpiryWithDays(singleService.expires_at)
    } : null,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    loading,
    error,
    refreshServices,
    updateAutoRenewal,
    totalServices: services.length,
    activeServices: services.filter(service => service.status === 'active').length,
    suspendedServices: services.filter(service => service.status === 'suspended').length,
    stoppedServices: services.filter(service => service.status === 'stopped').length,
    provisioningServices: services.filter(service => service.status === 'provisioning').length
  };
};
