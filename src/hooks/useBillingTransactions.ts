import { useState, useEffect } from 'react';
import { billingService, UserBalance, Transaction } from '../services/billingService';
import { useAuth } from '../context/AuthContext';
import { useSearchParams } from 'react-router-dom';

export const useBillingTransactions = () => {
  const { isAuthenticated } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const page = parseInt(searchParams.get('page') || '1');
  const perPage = 10; // Number of transactions per page

  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [loading, setLoading] = useState({
    balance: true,
    transactions: true
  });
  const [error, setError] = useState<string | null>(null);

  // Fetch user balance and transactions when authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      setUserBalance(null);
      setTransactions([]);
      return;
    }

    const fetchUserData = async () => {
      try {
        // Fetch user balance
        setLoading(prev => ({ ...prev, balance: true }));
        const balance = await billingService.getUserBalance();
        setUserBalance(balance);

        // Fetch transactions with pagination
        setLoading(prev => ({ ...prev, transactions: true }));
        const offset = (page - 1) * perPage;
        const { data: transactionsData, count } = await billingService.getTransactions(perPage, offset);

        // Set transactions and total count
        setTransactions(transactionsData);
        setTotalTransactions(count);

        setError(null);
      } catch (err) {
        console.error('Error fetching user transactions data:', err);
        setError('Failed to load transactions data');
      } finally {
        setLoading(prev => ({
          ...prev,
          balance: false,
          transactions: false
        }));
      }
    };

    fetchUserData();
  }, [isAuthenticated, page, perPage]);

  // Function to change page
  const setPage = (newPage: number) => {
    setSearchParams({ page: newPage.toString() });
  };

  // Function to refresh all data
  const refreshData = async () => {
    try {
      setLoading(prev => ({ ...prev, balance: true, transactions: true }));

      const balance = await billingService.getUserBalance();
      setUserBalance(balance);

      const offset = (page - 1) * perPage;
      const { data: transactionsData, count } = await billingService.getTransactions(perPage, offset);
      setTransactions(transactionsData);
      setTotalTransactions(count);

      setError(null);
    } catch (err) {
      console.error('Error refreshing transactions data:', err);
      setError('Failed to refresh transactions data');
    } finally {
      setLoading(prev => ({ ...prev, balance: false, transactions: false }));
    }
  };

  // Calculate total pages for transactions
  const totalTransactionPages = Math.max(1, Math.ceil(totalTransactions / perPage));

  return {
    userBalance,
    transactions,
    loading,
    error,
    refreshData,
    // Pagination properties for transactions
    pagination: {
      page,
      perPage,
      totalPages: totalTransactionPages,
      totalItems: totalTransactions,
      setPage
    }
  };
};
