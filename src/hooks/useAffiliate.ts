import { useState, useEffect } from 'react';
import { affiliateService, AffiliateStats, AffiliateReferral } from '../services/affiliateService';
import { useAuth } from '../context/AuthContext';

interface UseAffiliateReturn {
  stats: AffiliateStats | null;
  referrals: AffiliateReferral[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  totalCount: number;
  currentPage: number;
  itemsPerPage: number;
  loadReferrals: (page?: number) => Promise<void>;
  refreshStats: () => Promise<void>;
  processStoredReferralCode: (isNewUser?: boolean) => Promise<boolean>;
  generateReferralLink: () => string;
}

export const useAffiliate = (itemsPerPage: number = 50): UseAffiliateReturn => {
  const { user } = useAuth();
  const [stats, setStats] = useState<AffiliateStats | null>(null);
  const [referrals, setReferrals] = useState<AffiliateReferral[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  const loadStats = async () => {
    if (!user?.id) return;

    try {
      setError(null);
      const statsData = await affiliateService.getAffiliateStats(user.id);
      setStats(statsData);
    } catch (err) {
      console.error('Error loading affiliate stats:', err);
      setError('Failed to load affiliate statistics');
    }
  };

  const loadReferrals = async (page: number = 1) => {
    if (!user?.id) return;

    try {
      setLoading(true);
      setError(null);

      const offset = (page - 1) * itemsPerPage;
      const response = await affiliateService.getAffiliateReferrals(user.id, itemsPerPage, offset);

      if (response) {
        setReferrals(response.referrals);
        setHasMore(response.hasMore);
        setTotalCount(response.totalCount);
        setCurrentPage(page);
      } else {
        setError('Failed to load referrals');
      }
    } catch (err) {
      console.error('Error loading affiliate referrals:', err);
      setError('Failed to load referrals');
    } finally {
      setLoading(false);
    }
  };

  const refreshStats = async () => {
    await loadStats();
  };

  const processStoredReferralCode = async (isNewUser?: boolean): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      const success = await affiliateService.processStoredReferralCode(user.id, isNewUser);
      if (success) {
        // Refresh stats after processing referral code
        await loadStats();
      }
      return success;
    } catch (err) {
      console.error('Error processing stored referral code:', err);
      return false;
    }
  };

  const generateReferralLink = (): string => {
    if (!user?.id) return '';
    return affiliateService.generateReferralLink(user.id);
  };

  // Load initial data when user is available
  useEffect(() => {
    if (user?.id) {
      loadStats();
      loadReferrals(1);
    }
  }, [user?.id, itemsPerPage]);

  // Process stored referral code when user logs in (auto-detect if new user)
  useEffect(() => {
    if (user?.id) {
      const referralCode = localStorage.getItem('referralCode');
      if (referralCode) {
        // Let the function auto-detect if user is new based on creation time
        processStoredReferralCode();
      }
    }
  }, [user?.id]);

  return {
    stats,
    referrals,
    loading,
    error,
    hasMore,
    totalCount,
    currentPage,
    itemsPerPage,
    loadReferrals,
    refreshStats,
    processStoredReferralCode,
    generateReferralLink
  };
};
