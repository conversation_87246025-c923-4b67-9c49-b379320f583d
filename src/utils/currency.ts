/**
 * Currency conversion utilities for AI features
 */

import { supabase } from '../lib/supabase';

// Cache for exchange rate
let cachedRate: number | null = null;
let cacheTimestamp: number | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes TTL

/**
 * Fetch current exchange rate from API
 * No fallback - always hits API when cache is empty
 */
export const fetchExchangeRate = async (): Promise<number> => {
  // Check if we have a valid cached rate
  if (cachedRate && cacheTimestamp && (Date.now() - cacheTimestamp) < CACHE_DURATION) {
    return cachedRate;
  }

  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    throw new Error('No session available');
  }

  const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/ai/rates', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${session.access_token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  const rate = data.rates;

  if (!rate || typeof rate !== 'number') {
    throw new Error('Invalid rate response from API');
  }

  // Cache the rate for 5 minutes
  cachedRate = rate;
  cacheTimestamp = Date.now();

  return rate;
};

/**
 * Convert USD to IDR using current exchange rate
 */
export const convertUSDToIDR = async (usdAmount: number): Promise<number> => {
  const rate = await fetchExchangeRate();
  return usdAmount * rate;
};

/**
 * Convert IDR to USD using current exchange rate
 */
export const convertIDRToUSD = async (idrAmount: number): Promise<number> => {
  const rate = await fetchExchangeRate();
  return idrAmount / rate;
};

/**
 * Convert USD to IDR using provided exchange rate (synchronous)
 */
export const convertUSDToIDRWithRate = (usdAmount: number, rate: number): number => {
  return usdAmount * rate;
};

/**
 * Convert IDR to USD using provided exchange rate (synchronous)
 */
export const convertIDRToUSDWithRate = (idrAmount: number, rate: number): number => {
  return idrAmount / rate;
};

/**
 * Format USD amount with proper currency symbol
 * Shows more decimal places for small amounts to display precise values
 * For positive balances, shows up to 7 decimal places without rounding
 */
export const formatUSD = (amount: number, decimals: number = 2): string => {
  // For positive amounts, show up to 7 decimal places to display precise values
  let actualDecimals = decimals;
  if (amount > 0) {
    // For positive amounts, show up to 7 decimal places
    actualDecimals = 7;
  } else if (Math.abs(amount) < 0.01 && Math.abs(amount) > 0) {
    // For very small amounts, show up to 6 decimal places
    actualDecimals = 6;
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: actualDecimals,
    maximumFractionDigits: actualDecimals,
  }).format(amount);
};

/**
 * Format USD amount without currency symbol
 * Shows more decimal places for small amounts to display precise values
 * For positive balances, shows up to 7 decimal places without rounding
 */
export const formatUSDNumber = (amount: number, decimals: number = 2): string => {
  // For positive amounts, show up to 7 decimal places to display precise values
  let actualDecimals = decimals;
  if (amount > 0) {
    // For positive amounts, show up to 7 decimal places
    actualDecimals = 7;
  } else if (Math.abs(amount) < 0.01 && Math.abs(amount) > 0) {
    // For very small amounts, show up to 6 decimal places
    actualDecimals = 6;
  }

  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: actualDecimals,
    maximumFractionDigits: actualDecimals,
  }).format(amount);
};

/**
 * Calculate how much IDR is needed for a USD amount
 */
export const calculateIDRForUSD = async (usdAmount: number): Promise<{
  usdAmount: number;
  idrAmount: number;
  exchangeRate: number;
}> => {
  const exchangeRate = await fetchExchangeRate();
  return {
    usdAmount,
    idrAmount: convertUSDToIDRWithRate(usdAmount, exchangeRate),
    exchangeRate,
  };
};

/**
 * Calculate how much IDR is needed for a USD amount (synchronous with provided rate)
 */
export const calculateIDRForUSDWithRate = (usdAmount: number, exchangeRate: number): {
  usdAmount: number;
  idrAmount: number;
  exchangeRate: number;
} => {
  return {
    usdAmount,
    idrAmount: convertUSDToIDRWithRate(usdAmount, exchangeRate),
    exchangeRate,
  };
};

/**
 * Format token count with proper separators
 */
export const formatTokens = (tokens: number): string => {
  return new Intl.NumberFormat('en-US').format(tokens);
};

/**
 * Calculate cost per token in USD
 */
export const calculateTokenCost = (
  inputTokens: number,
  outputTokens: number,
  inputPricePerToken: number,
  outputPricePerToken: number
): number => {
  return (inputTokens * inputPricePerToken) + (outputTokens * outputPricePerToken);
};
