/**
 * Formats a number to Indonesian currency format with Rp prefix (Rp 1.000,00)
 * @param value - The number to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted string with Rp prefix
 */
export const formatIDR = (value: number, decimals: number = 2): string => {
  // Convert to string with fixed decimal places
  const fixed = value.toFixed(decimals);

  // Split into whole and decimal parts
  const [whole, decimal] = fixed.split('.');

  // Format the whole part with dots as thousand separators
  const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

  // Return the formatted number with Rp prefix and comma as decimal separator
  return `Rp ${formattedWhole}${decimal ? ',' + decimal : ''}`;
};

/**
 * Formats a number to Indonesian number format without currency prefix (1.000,00)
 * @param value - The number to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted string without currency prefix
 */
export const formatNumber = (value: number, decimals: number = 2): string => {
  // Convert to string with fixed decimal places
  const fixed = value.toFixed(decimals);

  // Split into whole and decimal parts
  const [whole, decimal] = fixed.split('.');

  // Format the whole part with dots as thousand separators
  const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

  // Return the formatted number with comma as decimal separator
  return `${formattedWhole}${decimal ? ',' + decimal : ''}`;
};

/**
 * Formats a UTC date string to local timezone with timezone indicator
 * @param dateString - The UTC date string from server
 * @param options - Formatting options
 * @returns Formatted date string with timezone indicator
 */
export const formatDateWithTimezone = (
  dateString: string,
  options: {
    includeTime?: boolean;
    dateFormat?: 'short' | 'long';
  } = {}
): string => {
  const { includeTime = false, dateFormat = 'short' } = options;

  // Check if dateString is valid
  if (!dateString || dateString.trim() === '') {
    throw new Error('Invalid date string provided');
  }

  // Handle different date formats from Supabase
  let date: Date;

  // Check if it's already in ISO format (ends with Z)
  if (dateString.includes('Z')) {
    date = new Date(dateString);
  }
  // Handle PostgreSQL timestamp format (YYYY-MM-DD HH:MM:SS.ssssss+00)
  else if (dateString.includes('+00')) {
    // Replace space and +00 with Z for proper ISO format
    const isoString = dateString.replace(/\s?\+00$/, 'Z').replace(' ', 'T');
    date = new Date(isoString);
  }
  // Handle PostgreSQL timestamp format without timezone (YYYY-MM-DD HH:MM:SS.ssssss)
  else if (dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)) {
    // Convert to ISO format and assume UTC
    const isoString = dateString.replace(' ', 'T') + 'Z';
    date = new Date(isoString);
  }
  // Handle other formats by trying to parse directly
  else {
    // Try adding Z if it looks like UTC timestamp
    date = new Date(dateString + (dateString.includes('T') ? 'Z' : ''));
  }

  // Check if date is valid
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string provided');
  }

  // Get timezone offset in hours
  const timezoneOffset = -date.getTimezoneOffset() / 60;
  const timezoneString = timezoneOffset >= 0 ? `GMT+${timezoneOffset}` : `GMT${timezoneOffset}`;

  let formattedDate = '';

  if (dateFormat === 'short') {
    formattedDate = date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  } else {
    formattedDate = date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  if (includeTime) {
    const formattedTime = date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    });
    formattedDate += ` ${formattedTime}`;
  }

  return `${formattedDate} (${timezoneString})`;
};
