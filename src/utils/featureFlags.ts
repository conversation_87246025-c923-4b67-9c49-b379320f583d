/**
 * Utility functions for checking feature flags from environment variables
 */

/**
 * Check if a feature flag is enabled
 * @param flag The name of the feature flag to check
 * @returns boolean indicating if the feature is enabled
 */
export const isFeatureEnabled = (flag: string): boolean => {
  const envVar = import.meta.env[`VITE_${flag}`];

  // If the environment variable doesn't exist, the feature is disabled
  if (envVar === undefined) {
    return false;
  }

  // If the environment variable is a string, check if it's 'true'
  if (typeof envVar === 'string') {
    return envVar.toLowerCase() === 'true';
  }

  // If the environment variable is a boolean, return it directly
  if (typeof envVar === 'boolean') {
    return envVar;
  }

  // Default to false for any other type
  return false;
};

/**
 * Feature flag names
 */
export const FeatureFlags = {
  VPS_FEATURE: 'VPS_FEATURE',
  PODS_FEATURE: 'PODS_FEATURE',
  AI_FEATURE: 'AI_FEATURE',
  AI_TRANSACTION_TAB: 'AI_TRANSACTION_TAB',
  // Service Detail Tabs
  TAB_SERVICE_MONITOR: 'TAB_SERVICE_MONITOR',
  TAB_SERVICE_LOGS: 'TAB_SERVICE_LOGS',
  TAB_SERVICE_UPGRADE: 'TAB_SERVICE_UPGRADE',
  TAB_SERVICE_DOCKER_IMAGE: 'TAB_SERVICE_DOCKER_IMAGE',
  TAB_SERVICE_ENV: 'TAB_SERVICE_ENV',
  TAB_SERVICE_DEPLOYMENT: 'TAB_SERVICE_DEPLOYMENT',
};
