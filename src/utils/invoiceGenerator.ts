import { Payment } from '../services/billingService';
import { formatIDR } from './formatters';

export interface InvoiceData {
  payment: Payment;
  userEmail: string;
  userName?: string;
  issueDate: string;
}

export const generateInvoiceHTML = (data: InvoiceData): string => {
  const { payment, userEmail, userName, issueDate } = data;
  const invoiceNumber = payment.paymentReference || `INV-${payment.id}`;
  
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${invoiceNumber}</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          background: white;
        }
        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 40px;
          padding-bottom: 20px;
          border-bottom: 2px solid #e5e7eb;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
        }
        .invoice-info {
          text-align: right;
        }
        .invoice-number {
          font-size: 18px;
          font-weight: bold;
          color: #1f2937;
        }
        .invoice-date {
          color: #6b7280;
          margin-top: 5px;
        }
        .billing-info {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 40px;
          margin-bottom: 40px;
        }
        .billing-section h3 {
          margin: 0 0 10px 0;
          color: #1f2937;
          font-size: 16px;
        }
        .billing-section p {
          margin: 5px 0;
          color: #6b7280;
        }
        .invoice-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 40px;
        }
        .invoice-table th,
        .invoice-table td {
          padding: 12px;
          text-align: left;
          border-bottom: 1px solid #e5e7eb;
        }
        .invoice-table th {
          background-color: #f9fafb;
          font-weight: 600;
          color: #1f2937;
        }
        .invoice-table .amount {
          text-align: right;
        }
        .total-section {
          display: flex;
          justify-content: flex-end;
          margin-bottom: 40px;
        }
        .total-box {
          background-color: #f9fafb;
          padding: 20px;
          border-radius: 8px;
          min-width: 300px;
        }
        .total-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        }
        .total-row.final {
          font-weight: bold;
          font-size: 18px;
          color: #1f2937;
          border-top: 2px solid #e5e7eb;
          padding-top: 10px;
          margin-top: 10px;
        }
        .status-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
        }
        .status-completed {
          background-color: #d1fae5;
          color: #065f46;
        }
        .status-pending {
          background-color: #fef3c7;
          color: #92400e;
        }
        .status-failed {
          background-color: #fee2e2;
          color: #991b1b;
        }
        .footer {
          text-align: center;
          color: #6b7280;
          font-size: 14px;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }
        @media print {
          body {
            padding: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">SumoPod</div>
        <div class="invoice-info">
          <div class="invoice-number">Invoice ${invoiceNumber}</div>
          <div class="invoice-date">${issueDate}</div>
        </div>
      </div>

      <div class="billing-info">
        <div class="billing-section">
          <h3>From:</h3>
          <p><strong>SumoPod</strong></p>
          <p>Cloud Infrastructure Platform</p>
          <p><EMAIL></p>
        </div>
        <div class="billing-section">
          <h3>To:</h3>
          <p><strong>${userName || userEmail}</strong></p>
          <p>${userEmail}</p>
        </div>
      </div>

      <table class="invoice-table">
        <thead>
          <tr>
            <th>Description</th>
            <th>Credits</th>
            <th class="amount">Amount</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Credit Purchase</td>
            <td>${payment.credits.toLocaleString()} credits</td>
            <td class="amount">${formatIDR(payment.amount, 0)}</td>
            <td>
              <span class="status-badge status-${payment.status}">
                ${payment.status}
              </span>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="total-section">
        <div class="total-box">
          <div class="total-row">
            <span>Subtotal:</span>
            <span>${formatIDR(payment.amount, 0)}</span>
          </div>
          <div class="total-row">
            <span>Tax:</span>
            <span>Rp 0</span>
          </div>
          <div class="total-row final">
            <span>Total:</span>
            <span>${formatIDR(payment.amount, 0)}</span>
          </div>
        </div>
      </div>

      <div class="footer">
        <p>Thank you for using SumoPod!</p>
        <p>Payment Reference: ${payment.paymentReference || 'N/A'}</p>
      </div>
    </body>
    </html>
  `;
};

export const downloadInvoiceAsPDF = async (data: InvoiceData) => {
  const html = generateInvoiceHTML(data);
  
  // Create a new window for printing
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    throw new Error('Unable to open print window. Please check your popup blocker.');
  }

  printWindow.document.write(html);
  printWindow.document.close();

  // Wait for the content to load
  printWindow.onload = () => {
    // Focus and print
    printWindow.focus();
    printWindow.print();
    
    // Close the window after printing (optional)
    setTimeout(() => {
      printWindow.close();
    }, 1000);
  };
};


