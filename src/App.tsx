import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import AppRoutes from './routes';
import { AuthProvider } from './context/AuthContext';
import { ProfileProvider } from './context/ProfileContext';
import { ToastProvider } from './context/ToastContext';
import WhatsAppWidget from './components/WhatsAppWidget';

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <ProfileProvider>
          <ToastProvider>
            <AppRoutes />
            <WhatsAppWidget phoneNumber="6285190052577" />
          </ToastProvider>
        </ProfileProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;