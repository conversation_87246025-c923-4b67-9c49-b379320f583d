export interface AIBalance {
  balance: number; // Can be negative, represents USD balance
}

export interface AIModel {
  model_name: string;
  input_cost_per_1m_tokens: number; // in USD per 1M tokens
  output_cost_per_1m_tokens: number; // in USD per 1M tokens
  provider: string;
  context_length: number;
}

export interface AIUsage {
  request_id: string;
  model: string;
  spend: number; // in USD
  total_tokens: number;
  prompt_tokens: number;
  completion_tokens: number;
  startTime: string;
  endTime: string;
  total_duration_seconds: number;
  total_tokens_per_second: number;
}

export interface AITopUpRequest {
  amount: number; // in USD
  idrAmount: number; // converted amount in IDR
  exchangeRate: number;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
}

export interface PlaygroundSession {
  id: string;
  modelId: string;
  messages: ChatMessage[];
  totalTokens: number;
  totalCost: number;
  createdAt: string;
}

export interface AIStats {
  totalRequests: number;
  totalTokensUsed: number;
  totalSpent: number; // in USD
  averageCostPerRequest: number;
  mostUsedModel: string;
}

export interface APIKey {
  id: string; // Unique identifier for the key
  key_name: string; // The actual API key (masked in UI)
  key_alias: string; // Name/alias of the key
  spend: number; // Amount already spent in USD
  max_budget: number | null; // Credit limit in USD, null means unlimited
}

export interface AITransaction {
  id: string;
  userId: string;
  type: 'topup' | 'usage' | 'refund';
  amount: number; // in USD
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  paymentMethod?: string;
  transactionId?: string;
  createdAt: string;
  completedAt?: string;
  metadata?: {
    usdAmount?: number;
    idrAmount?: number;
    exchangeRate?: number;
    paymentProvider?: string;
    invoiceUrl?: string;
  };
}
