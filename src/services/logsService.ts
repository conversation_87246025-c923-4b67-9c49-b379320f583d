import { supabase } from '../lib/supabase';

export interface LogsData {
  data: string;
}

/**
 * Clean ANSI escape codes from log data for better display
 */
export const cleanLogData = (logData: string): string => {
  // Remove ANSI escape codes (color codes, cursor movements, etc.)
  return logData.replace(/\x1b\[[0-9;]*[mGKH]/g, '');
};

/**
 * Parse and format individual log entry
 */
export const parseLogEntry = (logLine: string): {
  timestamp?: string;
  level?: string;
  component?: string;
  message: string;
  raw: string;
} => {
  const cleaned = cleanLogData(logLine.trim());

  // Pattern for timestamp [HH:MM:SS.mmm]
  const timestampMatch = cleaned.match(/^\[?(\d{2}:\d{2}:\d{2}(?:\.\d{3})?)\]?\s*/);

  // Pattern for log level (INFO, ERROR, WARN, DEBUG, etc.)
  const levelMatch = cleaned.match(/\b(INFO|ERROR|WARN|WARNING|DEBUG|TRACE|FATAL)\b/i);

  // Pattern for component (text in parentheses)
  const componentMatch = cleaned.match(/\(([^)]+)\)/);

  let message = cleaned;
  let timestamp = timestampMatch ? timestampMatch[1] : undefined;
  let level = levelMatch ? levelMatch[1].toUpperCase() : undefined;
  let component = componentMatch ? componentMatch[1] : undefined;

  // Remove parsed parts from message
  if (timestampMatch) {
    message = message.replace(timestampMatch[0], '');
  }

  // Clean up the message
  message = message
    .replace(/^\s*\[?\d{2}:\d{2}:\d{2}(?:\.\d{3})?\]?\s*/, '') // Remove any remaining timestamp
    .replace(/^\s*(INFO|ERROR|WARN|WARNING|DEBUG|TRACE|FATAL)\s*/i, '') // Remove level
    .replace(/^\s*\([^)]+\):\s*/, '') // Remove component
    .trim();

  return {
    timestamp,
    level,
    component,
    message: message || cleaned, // Fallback to cleaned if message is empty
    raw: cleaned
  };
};

/**
 * Format log data for better readability
 */
export const formatLogData = (logData: string): string => {
  const cleaned = cleanLogData(logData);

  // Split by lines and filter out empty lines
  const lines = cleaned.split('\n').filter(line => line.trim() !== '');

  // Parse and format each line
  const formattedLines = lines.map(line => {
    const parsed = parseLogEntry(line);

    // Create formatted line with consistent structure
    let formattedLine = '';

    if (parsed.timestamp) {
      formattedLine += `[${parsed.timestamp}] `;
    }

    if (parsed.level) {
      const levelPadded = parsed.level.padEnd(5);
      formattedLine += `${levelPadded} `;
    }

    if (parsed.component) {
      formattedLine += `(${parsed.component}) `;
    }

    formattedLine += parsed.message;

    return formattedLine || parsed.raw; // Fallback to raw if formatting fails
  });

  return formattedLines.join('\n');
};

/**
 * Get log level color class for styling
 */
export const getLogLevelColor = (level?: string): string => {
  if (!level) return 'text-gray-300';

  switch (level.toUpperCase()) {
    case 'ERROR':
    case 'FATAL':
      return 'text-red-400';
    case 'WARN':
    case 'WARNING':
      return 'text-yellow-400';
    case 'INFO':
      return 'text-blue-400';
    case 'DEBUG':
      return 'text-green-400';
    case 'TRACE':
      return 'text-purple-400';
    default:
      return 'text-gray-300';
  }
};

/**
 * Format log data with syntax highlighting
 */
export const formatLogDataWithHighlighting = (logData: string): Array<{
  timestamp?: string;
  level?: string;
  component?: string;
  message: string;
  levelColor: string;
}> => {
  const cleaned = cleanLogData(logData);
  const lines = cleaned.split('\n').filter(line => line.trim() !== '');

  return lines.map(line => {
    const parsed = parseLogEntry(line);
    return {
      ...parsed,
      levelColor: getLogLevelColor(parsed.level)
    };
  });
};

export const logsService = {
  /**
   * Get logs data for a service
   */
  async getServiceLogs(serviceId: string): Promise<LogsData | null> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      console.error('No session found for logs data');
      return null;
    }

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/logs', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service_id: serviceId
        })
      });

      if (!response.ok) {
        console.error('Failed to fetch logs data:', response.status, response.statusText);
        return null;
      }

      const data: LogsData = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching logs data:', error);
      return null;
    }
  },
};
