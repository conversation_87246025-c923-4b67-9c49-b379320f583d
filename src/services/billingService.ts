import { supabase } from '../lib/supabase';



export interface UserBalance {
  id: number;
  userId: string;
  credits: number;
  createdAt: string;
  updatedAt: string;
}

export interface Transaction {
  id: number;
  userId: string;
  amount: number;
  description: string;
  transactionType: 'purchase' | 'usage' | 'refund' | 'bonus';
  referenceId?: string;
  createdAt: string;
}

export interface Payment {
  id: number;
  userId: string;
  amount: number;
  credits: number;
  status: 'completed' | 'pending' | 'failed';
  paymentMethod?: string;
  paymentReference?: string;
  paymentUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export const billingService = {

  async getUserBalance(): Promise<UserBalance | null> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return null;
    }

    const { data, error } = await supabase
      .from('user_balances')
      .select('*')
      .eq('user_id', session.user.id)
      .single();

    if (error) {
      console.error('Error fetching user balance:', error);
      return null;
    }

    if (!data) return null;

    return {
      id: data.id,
      userId: data.user_id,
      credits: data.credits,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  },

  async getTransactions(limit: number = 10, offset: number = 0): Promise<{ data: Transaction[], count: number }> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return { data: [], count: 0 };
    }

    // Get the data with pagination
    const { data, error, count } = await supabase
      .from('transactions')
      .select('*', { count: 'exact' })
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }

    const transactions = (data || []).map(transaction => ({
      id: transaction.id,
      userId: transaction.user_id,
      amount: transaction.amount,
      description: transaction.description,
      transactionType: transaction.transaction_type,
      referenceId: transaction.reference_id,
      createdAt: transaction.created_at
    }));

    return {
      data: transactions,
      count: count || 0
    };
  },

  async getPayments(limit: number = 10, offset: number = 0): Promise<{ data: Payment[], count: number }> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return { data: [], count: 0 };
    }

    const { data, error, count } = await supabase
      .from('payments')
      .select('*', { count: 'exact' })
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }

    const payments = (data || []).map(payment => ({
      id: payment.id,
      userId: payment.user_id,
      amount: payment.amount,
      credits: payment.credits,
      status: payment.status,
      paymentMethod: payment.payment_method,
      paymentReference: payment.payment_reference,
      paymentUrl: payment.payment_url,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    }));

    return { data: payments, count: count || 0 };
  },



  // Function to check payment status
  async checkPaymentStatus(paymentId: number): Promise<{ status: 'completed' | 'pending' | 'failed'; error?: string }> {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return { status: 'failed', error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('payments')
        .select('status')
        .eq('id', paymentId)
        .eq('user_id', session.user.id)
        .single();

      if (error) {
        return { status: 'failed', error: 'Payment not found' };
      }

      return { status: data.status as 'completed' | 'pending' | 'failed' };
    } catch (error) {
      console.error('Error checking payment status:', error);
      return { status: 'failed', error: 'An unexpected error occurred' };
    }
  },

  // Function to mark payment as paid (completed)
  async markPaymentAsPaid(paymentId: number): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await supabase
        .from('payments')
        .update({
          status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentId)
        .eq('user_id', session.user.id);

      if (error) {
        console.error('Error marking payment as paid:', error);
        return { success: false, error: 'Failed to update payment status' };
      }

      return { success: true };
    } catch (error) {
      console.error('Error marking payment as paid:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }
};
