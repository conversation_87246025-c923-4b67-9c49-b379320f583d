import { supabase } from '../lib/supabase';
import { AIBalance, AIModel, AIUsage, AIStats, PlaygroundSession, APIKey, AITransaction } from '../types/ai';

export const aiService = {
  /**
   * Get user's AI balance in USD
   */
  async getAIBalance(): Promise<AIBalance | null> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return null;
    }

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/ai/balance', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        balance: data.balance || 0, // Ensure we have a number, default to 0
      };
    } catch (error) {
      console.error('Error fetching AI balance:', error);
      // Return null on error so components can handle the error state
      return null;
    }
  },

  /**
   * Get available AI models
   */
  async getAIModels(): Promise<{ data: AIModel[], count: number }> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return { data: [], count: 0 };
    }

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/ai/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: AIModel[] = await response.json();

      // API returns array, empty array means no data
      return {
        data: data || [],
        count: data ? data.length : 0,
      };
    } catch (error) {
      console.error('Error fetching AI models:', error);
      // Return empty result on error
      return {
        data: [],
        count: 0,
      };
    }
  },

  /**
   * Get AI usage history
   */
  async getAIUsage(): Promise<{ data: AIUsage[], count: number }> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return { data: [], count: 0 };
    }

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/ai/usage', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: AIUsage[] = await response.json();

      // API returns array, empty array means no data
      return {
        data: data || [],
        count: data ? data.length : 0,
      };
    } catch (error) {
      console.error('Error fetching AI usage:', error);
      // Return empty result on error
      return {
        data: [],
        count: 0,
      };
    }
  },

  /**
   * Get AI usage statistics
   */
  async getAIStats(): Promise<AIStats> {
    // Mock statistics
    return {
      totalRequests: 47,
      totalTokensUsed: 15420,
      totalSpent: 2.34,
      averageCostPerRequest: 0.0498,
      mostUsedModel: 'GPT-4o Mini',
    };
  },

  /**
   * Top up AI balance (convert IDR to USD)
   */
  async topUpAIBalance(usdAmount: number): Promise<{ success: boolean; error?: string; message?: string; code?: string }> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return { success: false, error: 'User not authenticated' };
    }

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/ai/topup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount_in_usd: usdAmount
        }),
      });

      if (response.status === 200) {
        return { success: true };
      } else {
        // Handle error responses
        try {
          const errorData = await response.json();
          return {
            success: false,
            error: errorData.message || 'Top-up failed',
            message: errorData.message,
            code: errorData.code
          };
        } catch (parseError) {
          return {
            success: false,
            error: `Top-up failed with status ${response.status}`
          };
        }
      }
    } catch (error) {
      console.error('Error during top-up:', error);
      return {
        success: false,
        error: 'Network error occurred during top-up'
      };
    }
  },

  /**
   * Send chat message to AI model (playground)
   */
  async sendChatMessage(
    modelId: string,
    messages: { role: string; content: string }[]
  ): Promise<{ success: boolean; response?: string; usage?: { inputTokens: number; outputTokens: number; cost: number }; error?: string }> {
    // Mock response for playground
    const mockResponses = [
      "I'm a mock AI response. In a real implementation, this would connect to the actual AI model API.",
      "This is a simulated response from the AI model. The playground feature would integrate with real AI APIs.",
      "Hello! I'm responding as a mock AI. This demonstrates the playground interface functionality.",
    ];

    const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];
    const mockInputTokens = Math.floor(Math.random() * 100) + 50;
    const mockOutputTokens = Math.floor(Math.random() * 150) + 75;
    const mockCost = (mockInputTokens * 0.000005) + (mockOutputTokens * 0.000015); // Using GPT-4o pricing

    return {
      success: true,
      response: randomResponse,
      usage: {
        inputTokens: mockInputTokens,
        outputTokens: mockOutputTokens,
        cost: mockCost,
      },
    };
  },

  /**
   * Get all user's API keys (max 100)
   */
  async getAPIKeys(): Promise<{ data: APIKey[], count: number }> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('Not authenticated');

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/ai/keys', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiKeys: APIKey[] = await response.json();

      return {
        data: apiKeys,
        count: apiKeys.length,
      };
    } catch (error) {
      console.error('Error fetching API keys:', error);
      // Return empty result on error
      return {
        data: [],
        count: 0,
      };
    }
  },

  /**
   * Create a new API key
   */
  async createAPIKey(name: string, creditLimit?: number): Promise<APIKey> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('Not authenticated');

    // First check if user already has 100 keys
    try {
      const { count } = await this.getAPIKeys();
      if (count >= 100) {
        throw new Error('Maximum of 100 API keys allowed. Please delete some keys before creating new ones.');
      }
    } catch (error) {
      // If it's our custom limit error, re-throw it
      if (error instanceof Error && error.message.includes('Maximum of 100 API keys')) {
        throw error;
      }
      // Otherwise, continue with creation (don't fail on fetch error)
    }

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/ai/keys', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name,
          budget: creditLimit || null,
        }),
      });

      if (!response.ok) {
        // For non-200 responses, show specific error message
        throw new Error('Your key name is already exist');
      }

      // On success (200), get the response data
      const responseData = await response.json();

      // Return the created key data with the actual key from the response
      const newAPIKey: APIKey = {
        id: responseData.id, // The unique identifier from the response
        key_name: responseData.key, // The actual API key from the response
        key_alias: name,
        spend: 0,
        max_budget: creditLimit || null,
      };

      return newAPIKey;
    } catch (error) {
      console.error('Error creating API key:', error);
      throw error;
    }
  },

  /**
   * Update an API key
   */
  async updateAPIKey(keyId: string, updates: { key_alias?: string; max_budget?: number | null }): Promise<void> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('Not authenticated');

    try {
      const payload = {
        key: keyId, // Use the ID, not the key_name
        budget: updates.max_budget,
        name: updates.key_alias,
      };

      console.log('Updating API key with payload:', payload);

      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/ai/keys/update', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      console.log('Update API key response status:', response.status);

      if (!response.ok) {
        // For non-200 responses, show error message
        const errorText = await response.text();
        console.error('Update API key error response:', errorText);
        throw new Error(`Failed to update API key: ${response.status}`);
      }

      console.log('API key updated successfully');
      // Success - no need to return anything for void function
    } catch (error) {
      console.error('Error updating API key:', error);
      throw error;
    }
  },

  /**
   * Delete an API key
   */
  async deleteAPIKey(keyId: string): Promise<void> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('Not authenticated');

    const response = await fetch(`https://api-gate.sumopod.com/webhook/sumopod/ai/keys/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: JSON.stringify({ key: keyId }),
    });

    if (!response.ok) {
      throw new Error('Failed to delete API key');
    }
  },

  /**
   * Get user's AI transactions with pagination
   */
  async getAITransactions(limit: number = 10, offset: number = 0): Promise<{ data: AITransaction[], count: number }> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('Not authenticated');

    // Mock transaction data - expanded for pagination
    const baseTransactions: AITransaction[] = [
      {
        id: '1',
        userId: session.user.id,
        type: 'topup',
        amount: 50.00,
        status: 'completed',
        description: 'Credit Topup via Balance',
        paymentMethod: undefined,
        transactionId: 'TXN-2024-001234',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 days ago
        completedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2 + 1000 * 60 * 30).toISOString(), // 30 min after created
        metadata: {
          usdAmount: 50.00,
          idrAmount: 825000,
          exchangeRate: 16500,
          paymentProvider: 'Midtrans',
          invoiceUrl: 'https://example.com/invoice/001234',
        },
      },
      {
        id: '2',
        userId: session.user.id,
        type: 'topup',
        amount: 25.00,
        status: 'completed',
        description: 'Credit Topup via Balance',
        paymentMethod: undefined,
        transactionId: 'TXN-2024-001235',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(), // 5 days ago
        completedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5 + 1000 * 60 * 5).toISOString(), // 5 min after created
        metadata: {
          usdAmount: 25.00,
          idrAmount: 412500,
          exchangeRate: 16500,
          paymentProvider: 'Midtrans',
          invoiceUrl: 'https://example.com/invoice/001235',
        },
      },
      {
        id: '3',
        userId: session.user.id,
        type: 'topup',
        amount: 100.00,
        status: 'completed',
        description: 'Credit Topup via Balance',
        paymentMethod: undefined,
        transactionId: 'TXN-2024-001236',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(), // 7 days ago
        completedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7 + 1000 * 60 * 10).toISOString(), // 10 min after created
        metadata: {
          usdAmount: 100.00,
          idrAmount: 1650000,
          exchangeRate: 16500,
          paymentProvider: 'Stripe',
          invoiceUrl: 'https://example.com/invoice/001236',
        },
      },
      {
        id: '4',
        userId: session.user.id,
        type: 'topup',
        amount: 75.00,
        status: 'completed',
        description: 'Credit Topup via Balance',
        paymentMethod: undefined,
        transactionId: 'TXN-2024-001237',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10).toISOString(), // 10 days ago
        completedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10 + 1000 * 60 * 15).toISOString(), // 15 min after created
        metadata: {
          usdAmount: 75.00,
          idrAmount: 1237500,
          exchangeRate: 16500,
          paymentProvider: 'Midtrans',
          invoiceUrl: 'https://example.com/invoice/001237',
        },
      },
      {
        id: '5',
        userId: session.user.id,
        type: 'topup',
        amount: 30.00,
        status: 'completed',
        description: 'Credit Topup via Balance',
        paymentMethod: undefined,
        transactionId: 'TXN-2024-001238',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago
        completedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15 + 1000 * 60 * 5).toISOString(), // 5 min after created
        metadata: {
          usdAmount: 30.00,
          idrAmount: 495000,
          exchangeRate: 16500,
          paymentProvider: 'Midtrans',
          invoiceUrl: 'https://example.com/invoice/001238',
        },
      },
    ];

    // Generate additional mock transactions for pagination testing
    const additionalTransactions = Array.from({ length: 20 }, (_, i) => {
      const providers = ['Midtrans', 'Stripe', 'Xendit'];
      const amounts = [10, 25, 50, 75, 100, 150, 200];

      const amount = amounts[i % amounts.length];
      const provider = providers[i % providers.length];
      const daysAgo = 20 + i;

      return {
        id: `${i + 6}`,
        userId: session.user.id,
        type: 'topup' as const,
        amount,
        status: 'completed' as const,
        description: 'Credit Topup via Balance',
        paymentMethod: undefined,
        transactionId: `TXN-2024-${String(1239 + i).padStart(6, '0')}`,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * daysAgo).toISOString(),
        completedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * daysAgo + 1000 * 60 * Math.floor(Math.random() * 30 + 5)).toISOString(),
        metadata: {
          usdAmount: amount,
          idrAmount: amount * 16500,
          exchangeRate: 16500,
          paymentProvider: provider,
          invoiceUrl: `https://example.com/invoice/${String(1239 + i).padStart(6, '0')}`,
        },
      };
    });

    const allTransactions = [...baseTransactions, ...additionalTransactions];

    // Sort by created date (newest first)
    const sortedTransactions = allTransactions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return {
      data: sortedTransactions.slice(offset, offset + limit),
      count: sortedTransactions.length,
    };
  },
};
