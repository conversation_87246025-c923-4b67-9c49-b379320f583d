import { supabase } from '../lib/supabase';

// Note: Database functions removed, using n8n endpoints for affiliate data

export interface AffiliateStats {
  totalReferrals: number;
  totalRegistered: number;
  totalTransacted: number;
  totalEarnings: number;
  thisMonthEarnings: number;
}

// API response interface for the summary endpoint
interface AffiliateSummaryResponse {
  total_refferals: number;
  total_registrations: number;
  total_transacted: number;
  total_earnings: number;
}

export interface AffiliateReferral {
  id: string;
  totalCommission: number;
  status: 'registered' | 'active';
  createdAt: string;
  expiresAt: string;
}

// API response interface for the transactions endpoint
interface AffiliateTransactionResponse {
  total_commission: string;
  status: 'registered' | 'active';
  created_at: string;
  expires_at: string;
  id: string;
}

export interface AffiliateReferralsResponse {
  referrals: AffiliateReferral[];
  totalCount: number;
  hasMore: boolean;
}

export const affiliateService = {

  /**
   * Get affiliate stats for a user from API
   */
  async getAffiliateStats(_userId: string): Promise<AffiliateStats | null> {
    try {
      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error('User not authenticated');
        return null;
      }

      // Call the affiliate summary API
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/affiliate/summary', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        console.error('API request failed:', response.status, response.statusText);
        return null;
      }

      const data: AffiliateSummaryResponse = await response.json();

      // Map API response to our interface
      return {
        totalReferrals: data.total_refferals,
        totalRegistered: data.total_registrations,
        totalTransacted: data.total_transacted,
        totalEarnings: data.total_earnings,
        thisMonthEarnings: 0 // Not provided by API, keeping as 0 for now
      };
    } catch (error) {
      console.error('Error fetching affiliate stats:', error);
      return null;
    }
  },

  /**
   * Get affiliate referrals from API (top 50 activities)
   */
  async getAffiliateReferrals(
    _userId: string,
    _limit: number = 50,
    _offset: number = 0
  ): Promise<AffiliateReferralsResponse | null> {
    try {
      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error('User not authenticated');
        return null;
      }

      // Call the affiliate transactions API
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/affiliate/transactions', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (!response.ok) {
        console.error('API request failed:', response.status, response.statusText);
        return null;
      }

      const data: AffiliateTransactionResponse[] = await response.json();

      // Map API response to our interface
      const referrals: AffiliateReferral[] = data.map(item => ({
        id: item.id,
        totalCommission: parseFloat(item.total_commission),
        status: item.status,
        createdAt: item.created_at,
        expiresAt: item.expires_at
      }));

      return {
        referrals,
        totalCount: referrals.length,
        hasMore: false // No pagination for top 50
      };
    } catch (error) {
      console.error('Error fetching affiliate referrals:', error);
      return null;
    }
  },

  /**
   * Check if user has referral code in localStorage and process it only for new users
   */
  async processStoredReferralCode(userId: string, isNewUser?: boolean): Promise<boolean> {
    try {
      const referralCode = localStorage.getItem('referralCode');

      if (!referralCode) {
        return false;
      }

      // If isNewUser is not explicitly provided, check if user was created recently (within last 5 minutes)
      let shouldProcess = isNewUser;

      if (shouldProcess === undefined) {
        try {
          const { data: { session } } = await supabase.auth.getSession();
          if (session?.user) {
            const userCreatedAt = new Date(session.user.created_at);
            const now = new Date();
            const timeDiffMinutes = (now.getTime() - userCreatedAt.getTime()) / (1000 * 60);

            // Consider user as "new" if created within last 5 minutes
            shouldProcess = timeDiffMinutes <= 5;
            console.log(`User created ${timeDiffMinutes.toFixed(2)} minutes ago, shouldProcess: ${shouldProcess}`);
          }
        } catch (error) {
          console.error('Error checking user creation time:', error);
          shouldProcess = false;
        }
      }

      // Only process referral code for new users
      if (!shouldProcess) {
        console.log('Referral code found but user is not new, skipping processing');
        // Still remove the referral code to prevent future processing
        localStorage.removeItem('referralCode');
        return false;
      }

      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        // Fire and forget affiliate verification
        fetch('https://api-gate.sumopod.com/webhook/sumopod/affiliate/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({ code: referralCode })
        }).catch(() => {});
      }

      // For now, just remove the referral code
      localStorage.removeItem('referralCode');
      return true;
    } catch (error) {
      console.error('Error processing stored referral code:', error);
      return false;
    }
  },

  /**
   * Get user's referral link
   */
  generateReferralLink(userId: string): string {
    const baseUrl = window.location.origin;
    return `${baseUrl}/register?ref=${userId}`;
  }
};
