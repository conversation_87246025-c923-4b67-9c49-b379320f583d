import { supabase } from '../lib/supabase';

export interface MonitorData {
  cpu: {
    percent: number;
  };
  memory: {
    usage: number;
    percent: number;
  };
  network: {
    in: number;
    out: number;
  };
}

export interface StorageData {
  size: number; // in bytes
}

export const monitorService = {
  /**
   * Get monitoring data for a service
   */
  async getServiceMonitorData(serviceId: string): Promise<MonitorData | null> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      console.error('No session found for monitoring data');
      return null;
    }

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/monitor', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service_id: serviceId
        })
      });

      if (!response.ok) {
        console.error('Failed to fetch monitoring data:', response.status, response.statusText);
        return null;
      }

      const data: MonitorData = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching monitoring data:', error);
      return null;
    }
  },

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * Format network traffic to human readable format
   */
  formatNetworkTraffic(bytes: number): string {
    return this.formatBytes(bytes);
  },

  /**
   * Format percentage to display format
   */
  formatPercentage(percent: number): string {
    return `${percent.toFixed(1)}%`;
  },

  /**
   * Get storage data for a service
   */
  async getServiceStorageData(serviceId: string): Promise<StorageData | null> {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      console.error('No session found for storage data');
      return null;
    }

    try {
      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/storage', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service_id: serviceId
        })
      });

      if (!response.ok) {
        console.error('Failed to fetch storage data:', response.status, response.statusText);
        return null;
      }

      const data: StorageData = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching storage data:', error);
      return null;
    }
  }
};
