import { supabase } from '../lib/supabase';

export interface DeploymentData {
  log: string;
  createdAt: string;
  description: string;
  status: 'done' | 'failed' | 'in_progress' | 'warning';
}

export interface EnvironmentData {
  env: string;
}

export const deploymentService = {
  /**
   * Get deployment list for a service
   */
  async getServiceDeployments(serviceId: string): Promise<DeploymentData[]> {
    try {
      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error('User not authenticated');
        return [];
      }

      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/deployments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          service_id: serviceId
        })
      });

      if (!response.ok) {
        console.error('Failed to fetch deployments:', response.status, response.statusText);
        return [];
      }

      const data: DeploymentData[] = await response.json();
      return data || [];
    } catch (error) {
      console.error('Error fetching service deployments:', error);
      return [];
    }
  },

  /**
   * Get environment variables for a service
   */
  async getServiceEnvironmentVariables(serviceId: string): Promise<EnvironmentData | null> {
    try {
      // Get the user's session for authorization
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error('User not authenticated');
        return null;
      }

      const response = await fetch('https://api-gate.sumopod.com/webhook/sumopod/services/get-env', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          service_id: serviceId
        })
      });

      if (!response.ok) {
        console.error('Failed to fetch environment variables:', response.status, response.statusText);
        return null;
      }

      const data: EnvironmentData = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching service environment variables:', error);
      return null;
    }
  }
};
