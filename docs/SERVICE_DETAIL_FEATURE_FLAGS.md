# Service Detail Feature Flags

This document describes the feature flags available for controlling the visibility of tabs and sections in the Service Details page.

## Overview

The Service Details page has been enhanced with granular feature flags that allow you to control which tabs and sections are visible to users. This provides flexibility for different deployment scenarios or user access levels.

## Feature Flags

### Tab-Level Feature Flags

These flags control the visibility of entire tabs in the service details page:

- **`VITE_TAB_SERVICE_MONITOR`** (default: `true`)
  - Controls the "Monitor" tab visibility
  - Shows resource usage, performance metrics, and monitoring charts
  - When disabled, the Monitor tab is completely hidden

- **`VITE_TAB_SERVICE_LOGS`** (default: `true`)
  - Controls the "Logs" tab visibility
  - Shows real-time service logs with filtering capabilities
  - When disabled, the Logs tab is completely hidden

- **`VITE_TAB_SERVICE_UPGRADE`** (default: `true`)
  - Controls the "Upgrade & Renew" tab visibility
  - Shows available plan upgrades and renewal options
  - When disabled, the Upgrade & Renew tab is completely hidden

### Configuration Tab Section Feature Flags

These flags control specific sections within the Configuration tab:

- **`VITE_TAB_SERVICE_DOCKER_IMAGE`** (default: `true`)
  - Controls the "Docker Image" section in Configuration tab
  - Shows current image, version selection, and update options
  - When disabled, the Docker Image section is hidden

- **`VITE_TAB_SERVICE_ENV`** (default: `true`)
  - Controls the "Environment Variables" section in Configuration tab
  - Shows environment variable management interface
  - When disabled, the Environment Variables section is hidden

- **`VITE_TAB_SERVICE_DEPLOYMENT`** (default: `true`)
  - Controls the "Recent Deployments" section in Configuration tab
  - Shows deployment history and logs
  - When disabled, the Recent Deployments section is hidden

## Always Visible Elements

The following tabs and sections are always visible regardless of feature flags:

- **Access Tab**: Always visible as it contains essential service connection information
- **Configuration Tab**: The tab itself is always visible, but its sections can be controlled
- **Service Details Section**: Basic service information in Configuration tab
- **Auto Renewal Settings Section**: Billing settings in Configuration tab

## Usage Examples

### Development Environment
```bash
# Enable all features for development
VITE_TAB_SERVICE_MONITOR=true
VITE_TAB_SERVICE_LOGS=true
VITE_TAB_SERVICE_UPGRADE=true
VITE_TAB_SERVICE_DOCKER_IMAGE=true
VITE_TAB_SERVICE_ENV=true
VITE_TAB_SERVICE_DEPLOYMENT=true
```

### Production Environment (Limited Access)
```bash
# Hide advanced features for regular users
VITE_TAB_SERVICE_MONITOR=true
VITE_TAB_SERVICE_LOGS=false
VITE_TAB_SERVICE_UPGRADE=true
VITE_TAB_SERVICE_DOCKER_IMAGE=false
VITE_TAB_SERVICE_ENV=false
VITE_TAB_SERVICE_DEPLOYMENT=false
```

### Basic User Environment
```bash
# Only show essential features
VITE_TAB_SERVICE_MONITOR=false
VITE_TAB_SERVICE_LOGS=false
VITE_TAB_SERVICE_UPGRADE=false
VITE_TAB_SERVICE_DOCKER_IMAGE=false
VITE_TAB_SERVICE_ENV=false
VITE_TAB_SERVICE_DEPLOYMENT=false
```

## Implementation Details

- Feature flags are checked using the `isFeatureEnabled()` utility function
- Tabs are conditionally rendered based on feature flag values
- Content sections within tabs are also conditionally rendered
- When a tab is disabled, both the tab button and its content are hidden
- The system gracefully handles missing or undefined environment variables (defaults to `false`)

## Notes

- Changes to feature flags require an application restart to take effect
- Feature flags only control UI visibility, not API access or permissions
- The Access tab and basic Configuration sections remain accessible for essential service management
- Consider user experience when disabling multiple tabs simultaneously
