# AI Models Feature

This document describes the AI Models feature implementation in SumoPod.

## Overview

The AI Models feature provides users with access to various AI models for text generation, code completion, and other AI-powered tasks. Users can manage their AI balance in USD, view usage statistics, explore available models, and test models in a playground environment.

## Feature Flag

The AI feature is controlled by the `VITE_AI_FEATURE` environment variable:

```bash
VITE_AI_FEATURE=true  # Enable AI feature
VITE_AI_FEATURE=false # Disable AI feature (default)
```

## Components

### 1. Main AI Dashboard (`/dashboard/ai`)
- Single page with tab-based navigation using query parameters
- Displays current USD balance and usage statistics
- Provides quick access to top-up functionality
- Default tab is "Usage" (`/dashboard/ai?tab=usage`)

### 2. Usage Tab (`/dashboard/ai?tab=usage`)
- Detailed breakdown of AI model usage per request
- Shows input/output tokens, costs, timestamps, and request types
- Filterable and searchable usage history
- **Default tab when accessing AI section**

### 3. Models Tab (`/dashboard/ai?tab=models`)
- Lists all available AI models with specifications
- Shows pricing per million tokens (input/output)
- Categorized by model type (text, code, image, multimodal)
- Provider information (OpenAI, Anthropic, Google)

### 4. Playground Tab (`/dashboard/ai?tab=playground`)
- Interactive chat interface for testing AI models
- Real-time cost tracking per session
- Model selection and configuration
- Message history and session management

## Currency System

### USD Balance
- AI services are priced in USD for international compatibility
- Users maintain a separate USD balance for AI services
- Exchange rate: 1 USD = 16,500 IDR (configurable)

### Top-Up Process
1. User selects USD amount to add to AI balance
2. System calculates equivalent IDR amount using current exchange rate
3. IDR amount is deducted from user's main balance
4. USD amount is added to AI balance
5. Transaction is recorded for both currencies

## Available Models

The system includes popular AI models from major providers:

### OpenAI
- **GPT-4o**: Most capable model for complex tasks
- **GPT-4o Mini**: Affordable model for lightweight tasks

### Anthropic
- **Claude 3.5 Sonnet**: Most intelligent model
- **Claude 3 Haiku**: Fastest and most compact model

### Google
- **Gemini 1.5 Pro**: Most capable multimodal model
- **Gemini 1.5 Flash**: Fast and versatile multimodal model

## Pricing Structure

Models are priced per token with separate rates for input and output:
- Input tokens: User prompts and context
- Output tokens: AI-generated responses
- Pricing varies by model capability and provider

## Technical Implementation

### File Structure
```
src/
├── pages/
│   ├── AI.tsx                 # Main AI dashboard with tab navigation
│   ├── AIUsage.tsx           # Usage history component (rendered in tab)
│   ├── AIModels.tsx          # Models catalog component (rendered in tab)
│   └── AIPlayground.tsx      # Interactive playground component (rendered in tab)
├── components/modals/
│   └── AITopUpModal.tsx      # USD top-up modal
├── services/
│   └── aiService.ts          # AI-related API calls
├── types/
│   └── ai.ts                 # TypeScript interfaces
└── utils/
    └── currency.ts           # Currency conversion utilities
```

### Key Features
- **Feature Flag Control**: Entire AI section can be enabled/disabled
- **Tab-based Navigation**: Single page with query parameter-based tabs
- **Default Usage Tab**: Opens to usage history by default
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Updates**: Balance and usage update in real-time
- **Mock Data**: Currently uses mock data for development/testing
- **Extensible**: Easy to add new models and providers

## Future Enhancements

1. **Real API Integration**: Connect to actual AI model APIs
2. **Database Tables**: Create proper database schema for AI data
3. **Advanced Analytics**: More detailed usage analytics and insights
4. **Model Fine-tuning**: Support for custom model training
5. **Batch Processing**: Support for bulk AI operations
6. **API Keys Management**: User-managed API keys for different providers

## Development Notes

- All AI functionality is currently mocked for UI development
- Currency conversion rate is hardcoded but easily configurable
- Feature follows existing SumoPod patterns and conventions
- Fully integrated with existing authentication and billing systems
